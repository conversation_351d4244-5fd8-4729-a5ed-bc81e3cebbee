# 4.1
def calculate_tax_elastic_modulus(current_date, data, threshold=0.2):
    """
    增值税税负变动率检查，增加过去3年的过滤条件

    参数:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含企业财税数据的字典

    返回:
        list: 包含每年税负率和税负变动率的列表，格式如下：
            [
                {"所属期": "2021", "税负率": "1.62%", "税负变动率": "88.37"},
                ...
            ]
        str: 风险描述，格式为“风险描述: 近三年内，{年份}税负率为 {税负率}%，较上期变动 {税负变动率}%，大幅波动可能存在申报错误或经营异常等问题。”
    """
    # 获取当前年份
    current_year = int(current_date[:4])
    # 计算过去3年
    start_year = current_year - 3

    value_added = data["data"]["valueAdded"]
    annual_data = {}

    for value in value_added:
        year = int(value["endDate"][:4])
        # 仅处理过去3年内的数据
        if year < start_year:
            continue

        # 销售额
        if value["projectType"] == "销售额":
            sales_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"sales_amt": sales_amt, "tax_amt": 0}
            else:
                annual_data[year]["sales_amt"] += sales_amt

        # 应纳税额
        if value["projectName"] == "应纳税额合计":
            tax_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"sales_amt": 0, "tax_amt": tax_amt}
            else:
                annual_data[year]["tax_amt"] += tax_amt

    # 计算税负率和税负变动率
    result = []
    risk_years = []
    previous_tax_rate = None
    all_zero = True  # 标记是否所有税负率和税负变动率都为0

    for year in sorted(annual_data.keys()):
        # 仅处理过去3年内的数据
        if year < start_year:
            continue

        temp = annual_data[year]["sales_amt"]
        tax_rate = annual_data[year]["tax_amt"] / temp if temp > 0 else 0
        tax_rate_percent = round(tax_rate * 100, 2)  # 转换为百分比并保留两位小数

        # 计算税负变动率
        if previous_tax_rate is not None:
            tax_rate_change = (
                round(((tax_rate - previous_tax_rate) / previous_tax_rate) * 100, 2)
                if previous_tax_rate != 0
                else 0
            )
        else:
            tax_rate_change = 0.00

        # 检查是否有非零的税负率或税负变动率
        if tax_rate_percent != 0 or tax_rate_change != 0:
            all_zero = False

        result.append(
            {
                "所属期": year,
                "税负率": f"{round(tax_rate_percent, 2)}%"
                if tax_rate_percent is not None
                else "--",
                "税负变动率": f"{round(tax_rate_change, 2)}%"
                if tax_rate_change is not None
                else "--",
            }
        )

        # 风险识别：连续三年内税负变动率超过±20%
        if abs(tax_rate_change) > threshold * 100.0:
            risk_years.append(
                {"年份": year, "税负率": tax_rate_percent, "税负变动率": tax_rate_change}
            )

        previous_tax_rate = tax_rate

    # 如果所有税负率和税负变动率都为0，则只输出标题
    if all_zero:
        result = ["所属期", "税负率", "税负变动率"]

    # 生成风险描述
    if risk_years:
        risk_desc = "风险描述: 近三年内，"
        for risk_year in risk_years:
            risk_desc += f"{risk_year['年份']}年税负率为 {risk_year['税负率']}%，较上期变动 {risk_year['税负变动率']}%，"
        risk_desc += f"大幅波动可能存在申报错误或经营异常等问题。"
    else:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


# 4.2
def calculate_income_tax_contribution_rate(data):
    """
    计算每年的企业所得税贡献率（企业所得税贡献率 = 应纳所得税额 / 营业收入 * 100）。
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、应纳所得税额和所得税贡献率的列表，格式如下：
            [
                {"所属期": "2021", "实际应纳所得税额（元）": 2944.5, "营业收入（元）": 236963.68, "企业所得税贡献率": "1.26%"},
                ...
            ]
        str: 风险描述，格式为“风险描述: 近三年内，{年份} 企业所得税贡献率为 {贡献率}%，大幅下降可能存在利润操纵或税收筹划不当等问题。”
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "tax": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取应纳所得税额
            if record["projectName"] == "八、实际应纳所得税额（28+29-30）":
                tax = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "tax": tax}
                else:
                    annual_data[year]["tax"] = tax

    # 计算企业所得税贡献率并格式化输出
    result = []
    risk_years = []  # 存储风险年份及相关信息
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        tax = annual_data[year]["tax"]
        income_tax_contribution_rate = (tax / revenue * 100) if revenue > 0 else 0.00
        income_tax_contribution_rate_formatted = (
            f"{round(income_tax_contribution_rate, 2)}%"
            if income_tax_contribution_rate is not None
            else "--"
        )  # 保留两位小数并添加百分号

        result.append(
            {
                "所属期": year,
                "实际应纳所得税额（元）": tax,
                "营业收入（元）": revenue,
                "企业所得税贡献率": income_tax_contribution_rate_formatted,
            }
        )

        # 风险识别：近三年内企业所得税贡献率小于 0.5%
        if income_tax_contribution_rate < 0.5:
            risk_years.append(
                {"年份": year, "企业所得税贡献率": income_tax_contribution_rate_formatted}
            )

    # 生成风险描述
    risk_desc = ""
    if risk_years:
        risk_desc = "风险描述: 近三年内，"
        for risk_year in risk_years:
            risk_desc += f"{risk_year['年份']}年企业所得税贡献率为 {risk_year['企业所得税贡献率']}，"
        risk_desc += f"大幅下降可能存在利润操纵或税收筹划不当等问题。"
    else:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


# 4.3
def calculate_taxable_income_rate(current_date, data):
    """
    计算每年的应税所得率（应税所得率 = 纳税调整后所得 / 营业收入 * 100），增加过去三年的过滤条件。

    参数:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含企业财税数据的字典

    返回:
        list: 包含每年营业收入、纳税调整后所得和应税所得率的列表，格式如下：
            [
                {"所属期": "2021", "纳税调整后所得（元）": 2944.5, "营业收入（元）": 236963.68, "应税所得率为": "1.26%"},
                ...
            ]
        str: 风险描述，格式为“风险描述: 近三年内，{年份} 应税所得率为 {所得率}%，大幅下降可能存在成本费用异常或利润操纵等问题。”
    """
    # 获取当前年份
    current_year = int(current_date[:4])

    # 计算过去3年
    start_year = current_year - 3

    # 提取 corporateIncome 数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和纳税调整后所得
    annual_data = {}

    # 遍历 corporateIncome 数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = int(record["endDate"][:4])  # 从 endDate 中提取年份

            # 仅处理过去 3 年的数据
            if year < start_year:
                continue

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "tax": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取纳税调整后所得
            if record["projectName"] == "四、纳税调整后所得（13-14+15-16-17+18）":
                tax = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "tax": tax}
                else:
                    annual_data[year]["tax"] = tax

    # 计算应税所得率并格式化输出
    result = []
    risk_years = []  # 存储风险年份及相关信息
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        tax = annual_data[year]["tax"]
        income_tax_contribution_rate = (tax / revenue * 100) if revenue > 0 else 0
        income_tax_contribution_rate_formatted = (
            f"{round(income_tax_contribution_rate, 2)}%"
            if income_tax_contribution_rate is not None
            else "--"
        )  # 保留两位小数并添加百分号

        result.append(
            {
                "所属期": str(year),
                "纳税调整后所得（元）": tax,
                "营业收入（元）": revenue,
                "应税所得率": income_tax_contribution_rate_formatted,
            }
        )

        # 风险识别：近三年内应税所得率小于 10%
        if income_tax_contribution_rate < 10:
            risk_years.append(
                {"年份": year, "应税所得率": income_tax_contribution_rate_formatted}
            )

    # 生成风险描述
    if risk_years:
        risk_desc = "风险描述: 近三年内，"
        for risk_year in risk_years:
            risk_desc += f"{risk_year['年份']}年应税所得率为{risk_year['应税所得率']}，"
        risk_desc += f"大幅下降可能存在成本费用异常或利润操纵等问题。"
    else:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


# 4.4
import json
from dateutil.relativedelta import relativedelta
from datetime import datetime
import re


def zero_declaration_detection(current_date, data):
    """
    增值税连续3个月零申报检测，增值税一般纳税人需关注“本期应补(退)税额”连续三个月是否等于零。
    参数:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含税款所属期起、税款所属期止和期间连续零申报次数的列表
        str: 风险描述
    """
    # 解析当前日期
    risk_desc: str = ""
    current_date_obj = datetime.strptime(current_date, "%Y%m")
    three_years_ago = current_date_obj - relativedelta(years=3)

    # 提取valueAdded数据
    income_records = data["data"]["valueAdded"]

    # 初始化结果列表和风险期间集合
    result = []
    risk_periods = []

    # 遍历valueAdded数据
    for record in income_records:
        # 检查是否为季度纳税申报表
        if record["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
            # 提取时间信息
            end_date_str = record["endDate"]
            end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S")

            # 过滤近三年外的记录
            if end_date_obj < three_years_ago:
                continue

            # 提取申报信息
            if record["projectName"] == "本期应补（退）税额":
                should_pay_amt = float(record["shouldPayAmt"] or 0)

                if should_pay_amt == 0.0:
                    period_info = {
                        "税款所属期起": record["beginDate"],
                        "税款所属期止": end_date_str,
                        "期间连续零申报次数": 3,  # 每个季度默认3个月
                    }
                    result.append(period_info)

                    # 格式化风险期间信息
                    start_month = record["beginDate"][:7].replace("-", ".")
                    end_month = end_date_str[:7].replace("-", ".")
                    risk_periods.append(f"{start_month}-{end_month}")

                    # 生成风险描述
                    risk_desc = "风险描述：该指标项未检测到风险"
                    if risk_periods:
                        period_list = "、".join([f"{p} 连续3个月" for p in risk_periods])
                    risk_desc = f"风险描述: 近三年内，{period_list} 零申报增值税，与企业实际经营情况可能存在不符。"
                else:
                    result.append(
                        {
                            "税款所属期起": record["beginDate"],
                            "税款所属期止": end_date_str,
                            "期间连续零申报次数": 0,  # 每个季度默认3个月
                        }
                    )
    # 正则表达式匹配日期范围
    pattern = r"(\d{4}\.\d{2})-(\d{4}\.\d{2}) 连续(\d+)个月"

    # 查找所有匹配项
    matches = re.findall(pattern, risk_desc)

    # 将匹配项转换为元组列表 (start_year_month, end_year_month, months)
    date_ranges = [
        (int(start.replace(".", "")), int(end.replace(".", "")), int(months))
        for start, end, months in matches
    ]

    # 按开始月份排序
    date_ranges.sort()
    merged_ranges = []
    current_range = None

    for start, end, months in date_ranges:
        if not current_range:
            current_range = (start, end, months)
        else:
            # 检查是否可以合并
            if start == current_range[1] + 1 and months == 3:
                current_range = (current_range[0], end, current_range[2] + months)
            else:
                merged_ranges.append(current_range)
                current_range = (start, end, months)

    if current_range:
        merged_ranges.append(current_range)

    # 构建新的描述字符串
    new_description = "风险描述: "
    for start, end, months in merged_ranges:
        start_str = f"{start // 100}.{start % 100}"
        end_str = f"{end // 100}.{end % 100}"
        new_description += f"{start_str}-{end_str}连续{months}个月,"

    # 去掉最后一个逗号并添加剩余部分
    new_description = new_description.rstrip(",") + "零申报增值税，与企业实际经营情况可能存在不符。"

    return result, new_description


# 4.5
def sales_volume_exceeded_detection(data):
    """
    小规模纳税人销售额超标检测，小规模纳税人短期内销售超过500万或连续多月零申报，收入额快速超过500万。
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年销售额的列表，格式如下：
            [
                {"所属期": "2021", "销售额（元）": 2944.5},
                ...
            ]
    """
    annual_data = {}

    value_added = data["data"]["valueAdded"]
    for value in value_added:
        # 0:一般纳税人 1:小规模纳税人 3:其他
        if value["taxpayerType"] != 1:
            continue

        year = value["endDate"][:4]
        # 销售额
        if value["projectName"] == "（一）应征增值税不含税销售额（3%征收率）":
            sales_amt = float(value["currentYearAccumulativeService"] or 0) + float(
                value["currentService"] or 0
            )
            if year not in annual_data:
                annual_data[year] = {"sales_amt": sales_amt}
            else:
                annual_data[year]["sales_amt"] += sales_amt

    # 将结果转换为指定格式
    result = [
        {"所属年度": year, "销售额（元）": annual_data[year]["sales_amt"]}
        for year in sorted(annual_data.keys())
    ]

    risk_years = []
    # 风险识别：销售额≤500万
    for year in sorted(annual_data.keys()):
        if annual_data[year]["sales_amt"] > 5000000:
            risk_years.append({"年份": year, "销售额": annual_data[year]["sales_amt"]})

    # 生成风险描述
    if risk_years:
        risk_desc = "风险描述: 近三年内，"
        for risk_year in risk_years:
            risk_desc += f"{risk_year['年份']}年，累计销售额已达到{risk_year['销售额']}元，"
            risk_desc += f"超过小规模纳税人标准。"
    else:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


# 4.6

from datetime import datetime, timedelta


def convert_to_year_month(date_str):
    # 使用 datetime 解析 YYYYMM 格式的日期
    date = datetime.strptime(date_str, "%Y%m")
    # 格式化为“*年*月”
    return date.strftime("%Y年%m月")


def begin_end_tax(current_date, data):
    """
    期初留抵税额与上期期末留抵税额核对分析
    参数:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含税款所属期起、税款所属期止、期初留抵税额和期末留抵税额的列表，格式如下：
            [
                {
                    '税款所属期起': "2024-01-01 00:00:00",
                    '税款所属期止': "2024-03-31 00:00:00",
                    '期初留抵税额（元）': 1234.5,
                    '期末留抵税额（元）': 1000.0
                },
                ...
            ]
        str: 风险描述
    """
    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")
    value_added = data["data"]["valueAdded"]

    # 使用字典存储每个时间段的累加结果
    tax_periods = {}
    all_zero = True

    # 初始化近 12 个月的月份列表
    months = []
    for i in range(12):
        month = current - timedelta(days=30 * i)
        months.append(month.strftime("%Y%m"))

    # 遍历近 12 个月的月份
    for month in months:
        for info in value_added:
            end_date = info["endDate"]
            begin_date = info["beginDate"]
            if not begin_date:
                continue

            # 提取日期并格式化为 'YYYYMM'
            date = begin_date[:7].replace("-", "")
            if date != month:
                continue

            # 获取期初留抵税额
            begin_tax_credit, end_tax_credit = 0, 0
            if info["projectName"] == "上期留抵税额":
                begin_tax_credit = float(info.get("shouldPayAmt", 0))
            elif info["projectName"] == "期末留抵税额":
                end_tax_credit = float(info.get("shouldPayAmt", 0))

            # 使用 (begin_date, end_date) 作为唯一键
            key = (begin_date, end_date)
            if key not in tax_periods:
                if begin_tax_credit != 0 or end_tax_credit != 0:
                    all_zero = False
                tax_periods[key] = {
                    "税款所属期起": begin_date,
                    "税款所属期止": end_date,
                    "期初留抵税额（元）": begin_tax_credit,
                    "期末留抵税额（元）": end_tax_credit,
                }

    # 将字典的值转换为列表，并按时间排序
    result = sorted(tax_periods.values(), key=lambda x: x["税款所属期起"], reverse=True)

    # 设置每个周期的 end_tax_credit 等于上一个周期的 begin_tax_credit
    if len(result) <= 1:
        risk_desc = f"⻛险描述：该指标项未检测到⻛险"
    else:
        risk_desc = "风险描述: "
        for i in range(len(result)):
            if i == 0:
                continue
            else:
                # 检查期初留抵税额与上月期末留抵税额是否一致
                if result[i]["期初留抵税额（元）"] != result[i - 1]["期末留抵税额（元）"]:
                    risk_desc += f"{convert_to_year_month(result[i]['税款所属期起'][:7])}、 申报表中期初留抵税额与上月期末留抵税额不一致。\n"
                else:
                    risk_desc = f"⻛险描述：该指标项未检测到⻛险"

    if all_zero:
        result = ["税款所属期起", "税款所属期止", "期初留抵税额（元）", "期末留抵税额（元）"]

    return result, risk_desc
