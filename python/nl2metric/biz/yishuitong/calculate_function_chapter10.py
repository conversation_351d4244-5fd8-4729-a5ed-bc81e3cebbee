import json
from collections import defaultdict
from decimal import Decimal
import math


def calculate_operating_assets(data):
    """
    计算运营资产，按年统计，并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、流动资产、流动负债和运营资产的字典列表，以及风险描述
    """
    # 存储每年的流动资产数据
    current_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的流动负债数据
    current_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 遍历资产负债表数据
    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 处理流动资产合计
            if record.get("projectName") == "流动资产合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动资产
                    # 累加期初和期末流动资产，并增加计数
                    current_assets_map[year]["initial_sum"] += initial_balance
                    current_assets_map[year]["ending_sum"] += ending_balance
                    current_assets_map[year]["count"] += 1
                except (TypeError, ValueError):
                    continue  # 忽略无效数据

            # 处理流动负债合计
            if record.get("projectName") == "流动负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动负债
                    # 累加期初和期末流动负债，并增加计数
                    current_liabilities_map[year]["initial_sum"] += initial_balance
                    current_liabilities_map[year]["ending_sum"] += ending_balance
                    current_liabilities_map[year]["count"] += 1
                except (TypeError, ValueError):
                    continue  # 忽略无效数据

    result = []
    risk_years = {}
    # 获取所有年份（流动资产和流动负债的年份取并集）
    years = sorted(
        set(current_assets_map.keys()).union(set(current_liabilities_map.keys()))
    )

    for year in years:
        # 计算平均流动资产
        average_current_assets = Decimal("0")
        if current_assets_map[year]["count"] > 0:
            average_current_assets = (
                current_assets_map[year]["initial_sum"]
                + current_assets_map[year]["ending_sum"]
            ) / (2 * current_assets_map[year]["count"])

        # 计算平均流动负债
        average_current_liabilities = Decimal("0")
        if current_liabilities_map[year]["count"] > 0:
            average_current_liabilities = (
                current_liabilities_map[year]["initial_sum"]
                + current_liabilities_map[year]["ending_sum"]
            ) / (2 * current_liabilities_map[year]["count"])

        # 计算运营资产: 流动资产 - 流动负债
        operating_assets = average_current_assets - average_current_liabilities

        # 若运营资产为负，则记录风险数据
        if operating_assets < 0:
            risk_years[year] = float(operating_assets)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "流动资产（元）": round(float(average_current_assets), 2),
                "流动负债（元）": round(float(average_current_liabilities), 2),
                "运营资产（元）": round(float(operating_assets), 2),
            }
        )

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, asset in risk_years.items():
            risk_desc += f"{year}年的运营资产为{round(asset, 2)}，运营资产为负值表明企业流动负债超过流动资产，"
        risk_desc += "短期偿债能力不足，需关注流动资产的质量和流动负债的规模，避免因资金链紧张而引发财务危机。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_current_ratio(data):
    """
    计算流动比率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含统计数据的列表和风险描述
    """
    # 设置风险阈值
    RISK_THRESHOLD = Decimal("1.5")

    # 存储每年的流动资产数据
    current_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的流动负债数据
    current_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 风险年份记录
    risk_years = {}

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "流动资产合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动资产

                    # 累加期初和期末流动资产，并增加计数
                    current_assets_map[year]["initial_sum"] += initial_balance
                    current_assets_map[year]["ending_sum"] += ending_balance
                    current_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            if record.get("projectName") == "流动负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动负债

                    # 累加期初和期末流动负债，并增加计数
                    current_liabilities_map[year]["initial_sum"] += initial_balance
                    current_liabilities_map[year]["ending_sum"] += ending_balance
                    current_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    # 获取所有年份（流动资产和流动负债的年份取并集）
    years = sorted(
        set(current_assets_map.keys()).union(set(current_liabilities_map.keys()))
    )

    for year in years:
        # 计算平均流动资产
        average_current_assets = Decimal("0")
        if current_assets_map[year]["count"] > 0:
            average_current_assets = (
                current_assets_map[year]["initial_sum"]
                + current_assets_map[year]["ending_sum"]
            ) / (2 * current_assets_map[year]["count"])

        # 计算平均流动负债
        average_current_liabilities = Decimal("0")
        if current_liabilities_map[year]["count"] > 0:
            average_current_liabilities = (
                current_liabilities_map[year]["initial_sum"]
                + current_liabilities_map[year]["ending_sum"]
            ) / (2 * current_liabilities_map[year]["count"])

        # 计算流动比率
        current_ratio = Decimal("0")
        if average_current_liabilities != 0:
            current_ratio = average_current_assets / average_current_liabilities
            # 添加风险判断
            if current_ratio < RISK_THRESHOLD:
                risk_years[year] = float(current_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "流动资产（元）": round(float(average_current_assets), 2),
                "流动负债（元）": round(float(average_current_liabilities), 2),
                "流动比率": round(float(current_ratio), 2),
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的流动比率为{round(ratio, 2)}，低于1.5，"
        risk_desc += "流动比率偏低可能表明企业短期偿债能力不足，存在流动资产不足或流动负债过高的问题，需关注企业的流动性和资金周转情况。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_quick_ratio(data):
    """
    计算速动比率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、速动资产、流动负债和速动比率的字典列表，以及风险描述
    """
    # 存储每年的流动资产数据
    current_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的存货数据
    inventory_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的流动负债数据
    current_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 风险年份记录：记录速动比率低于1的年份
    risk_years = {}

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取流动资产合计
            if record.get("projectName") == "流动资产合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动资产
                    current_assets_map[year]["initial_sum"] += initial_balance
                    current_assets_map[year]["ending_sum"] += ending_balance
                    current_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取存货
            if record.get("projectName") == "存货":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初存货
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末存货
                    inventory_map[year]["initial_sum"] += initial_balance
                    inventory_map[year]["ending_sum"] += ending_balance
                    inventory_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取流动负债合计
            if record.get("projectName") == "流动负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动负债
                    current_liabilities_map[year]["initial_sum"] += initial_balance
                    current_liabilities_map[year]["ending_sum"] += ending_balance
                    current_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    # 获取所有年份的并集（流动资产、存货和流动负债）
    years = sorted(
        set(current_assets_map.keys()).union(
            set(inventory_map.keys()), set(current_liabilities_map.keys())
        )
    )

    # 设置风险阈值，速动比率低于1，即认为存在风险
    RISK_THRESHOLD = Decimal("1")

    for year in years:
        # 计算平均流动资产
        average_current_assets = Decimal("0")
        if current_assets_map[year]["count"] > 0:
            average_current_assets = (
                current_assets_map[year]["initial_sum"]
                + current_assets_map[year]["ending_sum"]
            ) / (2 * current_assets_map[year]["count"])

        # 计算平均存货
        average_inventory = Decimal("0")
        if inventory_map[year]["count"] > 0:
            average_inventory = (
                inventory_map[year]["initial_sum"] + inventory_map[year]["ending_sum"]
            ) / (2 * inventory_map[year]["count"])

        # 计算平均流动负债
        average_current_liabilities = Decimal("0")
        if current_liabilities_map[year]["count"] > 0:
            average_current_liabilities = (
                current_liabilities_map[year]["initial_sum"]
                + current_liabilities_map[year]["ending_sum"]
            ) / (2 * current_liabilities_map[year]["count"])

        # 计算速动资产 = 流动资产 - 存货
        quick_assets = average_current_assets - average_inventory

        # 计算速动比率 = 速动资产 / 流动负债
        quick_ratio = Decimal("0")
        if average_current_liabilities != 0:
            quick_ratio = quick_assets / average_current_liabilities
            # 如果速动比率低于风险阈值，则记录风险数据
            if quick_ratio < RISK_THRESHOLD:
                risk_years[year] = float(quick_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "速动资产（元）": round(float(quick_assets), 2),
                "流动负债（元）": round(float(average_current_liabilities), 2),
                "速动比率": round(float(quick_ratio), 2),
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的速动比率为{round(ratio, 2)}，低于1，"
        risk_desc += (
            "速动比率偏低反映企业短期偿债能力较弱，即使剔除存货等流动性较差的资产后，企业可能难以偿还短期债务，需关注速动资产的质量及流动负债的管理。"
        )
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description


def calculate_cash_ratio(data):
    """
    计算现金比率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、货币资金、流动负债和现金比率的字典列表，以及风险描述
    """
    # 存储每年的货币资金数据
    cash_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的流动负债数据
    current_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 风险年份记录：记录现金比率低于风险阈值的年份
    risk_years = {}
    RISK_THRESHOLD = Decimal("0.2")

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取货币资金
            if record.get("projectName") == "货币资金":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初货币资金
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末货币资金
                    cash_map[year]["initial_sum"] += initial_balance
                    cash_map[year]["ending_sum"] += ending_balance
                    cash_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取流动负债合计
            if record.get("projectName") == "流动负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动负债
                    current_liabilities_map[year]["initial_sum"] += initial_balance
                    current_liabilities_map[year]["ending_sum"] += ending_balance
                    current_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    # 获取所有年份（货币资金和流动负债的年份取并集）
    years = sorted(set(cash_map.keys()).union(set(current_liabilities_map.keys())))

    for year in years:
        # 计算平均货币资金
        average_cash = Decimal("0")
        if cash_map[year]["count"] > 0:
            average_cash = (
                cash_map[year]["initial_sum"] + cash_map[year]["ending_sum"]
            ) / (2 * cash_map[year]["count"])

        # 计算平均流动负债
        average_current_liabilities = Decimal("0")
        if current_liabilities_map[year]["count"] > 0:
            average_current_liabilities = (
                current_liabilities_map[year]["initial_sum"]
                + current_liabilities_map[year]["ending_sum"]
            ) / (2 * current_liabilities_map[year]["count"])

        # 计算现金比率
        cash_ratio = Decimal("0")
        if average_current_liabilities != 0:
            cash_ratio = average_cash / average_current_liabilities
            # 添加风险判断
            if cash_ratio < RISK_THRESHOLD:
                risk_years[year] = float(cash_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "货币资金（元）": round(float(average_cash), 2),
                "流动负债（元）": round(float(average_current_liabilities), 2),
                "现金比率": round(float(cash_ratio), 2),
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的现金比率为{round(ratio, 2)}，低于0.2，"
        risk_desc += "现金比率过低表明企业现金储备不足，可能导致短期偿债压力较大，需关注货币资金的管理及企业的流动性风险。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description


def calculate_debt_to_asset_ratio(data):
    """
    计算资产负债率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、总负债、总资产和资产负债率的字典列表，以及风险描述
    """
    # 存储每年的总负债数据
    total_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的总资产数据
    total_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取总负债
            if record.get("projectName") == "负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初总负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末总负债

                    # 累加期初和期末总负债，并增加计数
                    total_liabilities_map[year]["initial_sum"] += initial_balance
                    total_liabilities_map[year]["ending_sum"] += ending_balance
                    total_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取总资产
            if record.get("projectName") == "资产总计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初总资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末总资产

                    # 累加期初和期末总资产，并增加计数
                    total_assets_map[year]["initial_sum"] += initial_balance
                    total_assets_map[year]["ending_sum"] += ending_balance
                    total_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    risk_years = {}
    # 获取所有年份（总负债和总资产的年份取并集）
    years = sorted(
        set(total_liabilities_map.keys()).union(set(total_assets_map.keys()))
    )

    for year in years:
        # 计算平均总负债
        average_total_liabilities = Decimal("0")
        if total_liabilities_map[year]["count"] > 0:
            average_total_liabilities = (
                total_liabilities_map[year]["initial_sum"]
                + total_liabilities_map[year]["ending_sum"]
            ) / (2 * total_liabilities_map[year]["count"])

        # 计算平均总资产
        average_total_assets = Decimal("0")
        if total_assets_map[year]["count"] > 0:
            average_total_assets = (
                total_assets_map[year]["initial_sum"]
                + total_assets_map[year]["ending_sum"]
            ) / (2 * total_assets_map[year]["count"])

        # 计算资产负债率
        debt_to_asset_ratio = Decimal("0")
        if average_total_assets != 0:
            debt_to_asset_ratio = (
                average_total_liabilities / average_total_assets
            ) * Decimal("100")

        # 若资产负债率高于70%，记录风险数据
        if debt_to_asset_ratio > Decimal("70"):
            risk_years[year] = f"{round(float(debt_to_asset_ratio), 2)}%"

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "总负债（元）": round(float(average_total_liabilities), 2),
                "总资产（元）": round(float(average_total_assets), 2),
                "资产负债率": f"{round(float(debt_to_asset_ratio), 2)}%",
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的资产负债率为{ratio}，高于70%，"
        risk_desc += "资产负债率过高可能表明企业负债水平过高，偿债压力较大，或存在财务杠杆过度使用的风险，需重点关注企业的债务结构和偿债能力。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description


def calculate_long_term_debt_to_capital_ratio(data):
    """
    计算长期资本负债率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、非流动负债、股东权益和长期资本负债率的字典列表，以及风险描述
    """
    # 存储每年的非流动负债数据
    non_current_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的股东权益数据
    shareholder_equity_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取非流动负债
            if record.get("projectName") == "非流动负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初非流动负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末非流动负债

                    # 累加期初和期末非流动负债，并增加计数
                    non_current_liabilities_map[year]["initial_sum"] += initial_balance
                    non_current_liabilities_map[year]["ending_sum"] += ending_balance
                    non_current_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取股东权益合计
            if record.get("projectName") == "所有者权益（或股东权益）合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初股东权益
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末股东权益

                    # 累加期初和期末股东权益，并增加计数
                    shareholder_equity_map[year]["initial_sum"] += initial_balance
                    shareholder_equity_map[year]["ending_sum"] += ending_balance
                    shareholder_equity_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    risk_years = {}
    # 获取所有年份（非流动负债和股东权益的年份取并集）
    years = sorted(
        set(non_current_liabilities_map.keys()).union(
            set(shareholder_equity_map.keys())
        )
    )

    # 设置风险阈值：长期资本负债率高于50%被视为风险
    RISK_THRESHOLD = Decimal("50")

    for year in years:
        # 计算平均非流动负债
        average_non_current_liabilities = Decimal("0")
        if non_current_liabilities_map[year]["count"] > 0:
            average_non_current_liabilities = (
                non_current_liabilities_map[year]["initial_sum"]
                + non_current_liabilities_map[year]["ending_sum"]
            ) / (2 * non_current_liabilities_map[year]["count"])

        # 计算平均股东权益
        average_shareholder_equity = Decimal("0")
        if shareholder_equity_map[year]["count"] > 0:
            average_shareholder_equity = (
                shareholder_equity_map[year]["initial_sum"]
                + shareholder_equity_map[year]["ending_sum"]
            ) / (2 * shareholder_equity_map[year]["count"])

        # 计算长期资本负债率
        total_capital = average_non_current_liabilities + average_shareholder_equity
        long_term_debt_to_capital_ratio = Decimal("0")
        if total_capital != 0:
            long_term_debt_to_capital_ratio = (
                average_non_current_liabilities / total_capital
            ) * Decimal("100")

        # 若长期资本负债率高于风险阈值，则记录风险数据
        if long_term_debt_to_capital_ratio > RISK_THRESHOLD:
            risk_years[year] = f"{round(float(long_term_debt_to_capital_ratio), 2)}%"

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "非流动负债（元）": round(float(average_non_current_liabilities), 2),
                "股东权益（元）": round(float(average_shareholder_equity), 2),
                "长期资本负债率（%）": f"{round(float(long_term_debt_to_capital_ratio), 2)}%",
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的长期资本负债率为{ratio}，高于50%，"
        risk_desc += "长期资本负债率过高表明企业长期资金来源中负债比例较高，可能导致企业资本结构不合理，需关注长期债务的管理及股东权益的补充。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description


def calculate_equity_ratio(data):
    """
    计算产权比率，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、总负债、股东权益和产权比率的字典列表，以及风险描述
    """
    # 存储每年的总负债数据
    total_liabilities_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的股东权益数据
    shareholder_equity_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取总负债
            if record.get("projectName") == "负债合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初总负债
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末总负债

                    # 累加期初和期末总负债，并增加计数
                    total_liabilities_map[year]["initial_sum"] += initial_balance
                    total_liabilities_map[year]["ending_sum"] += ending_balance
                    total_liabilities_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取股东权益合计
            if record.get("projectName") == "所有者权益（或股东权益）合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初股东权益
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末股东权益

                    # 累加期初和期末股东权益，并增加计数
                    shareholder_equity_map[year]["initial_sum"] += initial_balance
                    shareholder_equity_map[year]["ending_sum"] += ending_balance
                    shareholder_equity_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    risk_years = {}
    # 获取所有年份（总负债和股东权益的年份取并集）
    years = sorted(
        set(total_liabilities_map.keys()).union(set(shareholder_equity_map.keys()))
    )

    # 设置风险阈值：产权比率高于1.5被视为风险
    RISK_THRESHOLD = Decimal("1.5")

    for year in years:
        # 计算平均总负债
        average_total_liabilities = Decimal("0")
        if total_liabilities_map[year]["count"] > 0:
            average_total_liabilities = (
                total_liabilities_map[year]["initial_sum"]
                + total_liabilities_map[year]["ending_sum"]
            ) / (2 * total_liabilities_map[year]["count"])

        # 计算平均股东权益
        average_shareholder_equity = Decimal("0")
        if shareholder_equity_map[year]["count"] > 0:
            average_shareholder_equity = (
                shareholder_equity_map[year]["initial_sum"]
                + shareholder_equity_map[year]["ending_sum"]
            ) / (2 * shareholder_equity_map[year]["count"])

        # 计算产权比率：总负债/股东权益
        equity_ratio = Decimal("0")
        if average_shareholder_equity != 0:
            equity_ratio = average_total_liabilities / average_shareholder_equity

        # 若产权比率高于风险阈值，则记录风险数据
        if equity_ratio > RISK_THRESHOLD:
            risk_years[year] = round(float(equity_ratio), 2)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "总负债（元）": round(float(average_total_liabilities), 2),
                "股东权益（元）": round(float(average_shareholder_equity), 2),
                "产权比率": round(float(equity_ratio), 2),
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的产权比率为{ratio}，高于1.5，"
        risk_desc += "产权比率过高表明企业总负债相较于股东权益比例偏高，可能导致企业财务风险增加，需关注债务偿还能力及资本结构的优化。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description


def calculate_equity_multiplier(data):
    """
    计算权益乘数，按年统计并进行风险识别

    Args:
        data (dict): 包含资产负债表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、总资产、股东权益和权益乘数的字典列表，以及风险描述
    """
    # 存储每年的总资产数据
    total_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    # 存储每年的股东权益数据
    shareholder_equity_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )

    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份

            # 提取总资产
            if record.get("projectName") == "资产总计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初总资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末总资产

                    # 累加期初和期末总资产，并增加计数
                    total_assets_map[year]["initial_sum"] += initial_balance
                    total_assets_map[year]["ending_sum"] += ending_balance
                    total_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

            # 提取股东权益合计
            if record.get("projectName") == "所有者权益（或股东权益）合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初股东权益
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末股东权益

                    # 累加期初和期末股东权益，并增加计数
                    shareholder_equity_map[year]["initial_sum"] += initial_balance
                    shareholder_equity_map[year]["ending_sum"] += ending_balance
                    shareholder_equity_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    risk_years = {}
    # 获取所有年份（总资产和股东权益的年份取并集）
    years = sorted(
        set(total_assets_map.keys()).union(set(shareholder_equity_map.keys()))
    )

    # 设置风险阈值：权益乘数高于2.5被视为风险
    RISK_THRESHOLD = Decimal("2.5")

    for year in years:
        # 计算平均总资产
        average_total_assets = Decimal("0")
        if total_assets_map[year]["count"] > 0:
            average_total_assets = (
                total_assets_map[year]["initial_sum"]
                + total_assets_map[year]["ending_sum"]
            ) / (2 * total_assets_map[year]["count"])

        # 计算平均股东权益
        average_shareholder_equity = Decimal("0")
        if shareholder_equity_map[year]["count"] > 0:
            average_shareholder_equity = (
                shareholder_equity_map[year]["initial_sum"]
                + shareholder_equity_map[year]["ending_sum"]
            ) / (2 * shareholder_equity_map[year]["count"])

        # 计算权益乘数 = 总资产 / 股东权益
        equity_multiplier = Decimal("0")
        if average_shareholder_equity != 0:
            equity_multiplier = average_total_assets / average_shareholder_equity

        # 若权益乘数高于风险阈值，则记录风险数据
        if equity_multiplier > RISK_THRESHOLD:
            risk_years[year] = round(float(equity_multiplier), 2)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "总资产（元）": round(float(average_total_assets), 2),
                "股东权益（元）": round(float(average_shareholder_equity), 2),
                "权益乘数": round(float(equity_multiplier), 2),
            }
        )

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, multiplier in risk_years.items():
            risk_desc += f"{year}年的权益乘数为{multiplier}，高于2.5，"
        risk_desc += "权益乘数过高反映企业资产对股东权益的依赖较低，但可能伴随较高的财务杠杆，需关注企业是否过度依赖负债融资，增加财务风险。"
        return risk_desc

    risk_description = generate_risk_description(risk_years)
    return result, risk_description
