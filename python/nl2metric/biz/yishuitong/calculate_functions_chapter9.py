import json
from collections import defaultdict
from decimal import Decimal
import math


def calculate_receivables_turnover(data):
    """
    计算应收账款周转率和应收账款周转天数，按年统计，并进行风险识别

    Args:
        data (dict): 包含资产负债表和利润表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、营业收入、平均应收账款、周转率和周转天数的字典列表，以及风险描述
    """
    revenue_map = defaultdict(Decimal)  # 存储每年的营业收入
    receivables_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )  # 存储每年的期初和期末应收账款总和及提取次数

    # 提取营业收入
    for record in data["data"]["financeProfit"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "一、营业收入":
                try:
                    revenue = Decimal(str(record.get("currentPeriodAmount", 0)))
                    revenue_map[year] += revenue
                except:
                    continue  # 忽略无效数据

    # 提取应收账款
    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            end_date = record.get("endDate", "")
            year = end_date[:4]  # 从 endDate 中提取年份

            if record.get("projectName") == "应收账款":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初余额
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末余额

                    # 累加期初和期末应收账款，并增加计数
                    receivables_map[year]["initial_sum"] += initial_balance
                    receivables_map[year]["ending_sum"] += ending_balance
                    receivables_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    # 获取所有年份、排序
    years = sorted(set(revenue_map.keys()).union(set(receivables_map.keys())))

    # 风险识别数据：存储风险年份及对应的周转率
    risk_years = {}

    for year in years:
        # 获取营业收入
        revenue = revenue_map.get(year, Decimal("0"))

        # 获取期初和期末应收账款的总和及提取次数
        initial_sum = receivables_map.get(year, {}).get("initial_sum", Decimal("0"))
        ending_sum = receivables_map.get(year, {}).get("ending_sum", Decimal("0"))
        count = receivables_map.get(year, {}).get("count", 0)

        # 计算平均应收账款
        average_receivables = Decimal("0")
        if count > 0:
            average_receivables = (initial_sum + ending_sum) / (2 * count)

        # 计算应收账款周转率
        turnover_ratio = Decimal("0")
        if average_receivables != 0:
            turnover_ratio = revenue / average_receivables

        # 计算应收账款周转天数
        turnover_days = Decimal("0")
        if turnover_ratio != 0:
            turnover_days = Decimal("365") / turnover_ratio
            turnover_days = Decimal(math.ceil(turnover_days))

        # 如果周转率低于行业标准 (5次/年)，记录风险数据
        # 这里行业标准为5次/年，低于该标准即视为风险
        if turnover_ratio < Decimal("5"):
            risk_years[year] = float(turnover_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(float(revenue), 2),
                "平均应收账款（元）": round(float(average_receivables), 2),
                "应收账款周转率": round(float(turnover_ratio), 2),
                "应收账款周转天数": int(turnover_days),
            }
        )

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的应收账款周转率为{round(ratio, 2)}，低于行业标准的5次/年，"
        risk_desc += (
            "应收账款周转率偏低或周转天数过长可能表明企业回款能力较差，存在信用政策宽松或客户质量不佳的问题，这可能导致坏账风险，需关注应收账款管理及账龄情况。"
        )
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_inventory_turnover(data):
    """
    计算存货周转率和存货周转天数，按年统计，并进行风险识别

    Args:
        data (dict): 包含资产负债表和利润表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、营业收入、平均存货、存货周转率和存货周转天数的字典列表，以及风险描述
    """
    cost_map = defaultdict(Decimal)
    inventory_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )  # 存储每年的期初和期末存货总和及提取次数

    # 提取营业成本（这里取营业收入中的营业成本数据）
    for record in data["data"]["financeProfit"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "一、营业收入":
                try:
                    cost_of_goods_sold = Decimal(
                        str(record.get("currentPeriodAmount", 0))
                    )
                    cost_map[year] += cost_of_goods_sold
                except:
                    continue  # 忽略无效数据

    # 提取存货
    for record in data["data"]["financeBalance"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "存货":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初存货
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末存货

                    # 累加期初和期末存货，并增加计数
                    inventory_map[year]["initial_sum"] += initial_balance
                    inventory_map[year]["ending_sum"] += ending_balance
                    inventory_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    years = sorted(set(cost_map.keys()).union(set(inventory_map.keys())))

    # 风险识别数据：记录风险年份及对应的存货周转率
    risk_years = {}
    INDUSTRY_STANDARD = Decimal("3")  # 行业标准：3次/年

    for year in years:
        # 获取营业成本（营业收入中对应的成本数据）
        cost_of_goods_sold = cost_map.get(year, Decimal("0"))

        # 获取期初和期末存货的总和及提取次数
        initial_sum = inventory_map.get(year, {}).get("initial_sum", Decimal("0"))
        ending_sum = inventory_map.get(year, {}).get("ending_sum", Decimal("0"))
        count = inventory_map.get(year, {}).get("count", 0)

        # 计算平均存货
        average_inventory = Decimal("0")
        if count > 0:
            average_inventory = (initial_sum + ending_sum) / (2 * count)

        # 计算存货周转率
        turnover_ratio = Decimal("0")
        if average_inventory != 0:
            turnover_ratio = cost_of_goods_sold / average_inventory

        # 计算存货周转天数
        turnover_days = Decimal("0")
        if turnover_ratio != 0:
            turnover_days = Decimal("365") / turnover_ratio
            turnover_days = Decimal(math.ceil(turnover_days))

        # 如果存货周转率低于行业标准，则记录风险数据
        if turnover_ratio < INDUSTRY_STANDARD:
            risk_years[year] = float(turnover_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(float(cost_of_goods_sold), 2),
                "平均存货（元）": round(float(average_inventory), 2),
                "存货周转率": round(float(turnover_ratio), 2),
                "存货周转天数": int(turnover_days),
            }
        )

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的存货周转率为{round(ratio, 2)}，低于行业标准的3次/年，"
        risk_desc += (
            "存货周转率偏低或周转天数过长可能反映企业存货积压、产品滞销或库存管理不善，这可能导致资金占用过大或存货减值风险，需关注存货结构和管理效率。"
        )
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_current_assets_turnover(data):
    """
    计算流动资产周转率和流动资产周转天数，按年统计，并进行风险识别

    Args:
        data (dict): 包含资产负债表和利润表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、营业收入、平均流动资产、流动资产周转率和流动资产周转天数的字典列表，以及风险描述
    """
    revenue_map = defaultdict(Decimal)  # 存储每年的营业收入
    current_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )  # 存储每年的期初和期末流动资产总和及提取次数

    # 提取营业收入
    for record in data["data"]["financeProfit"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "一、营业收入":
                try:
                    revenue = Decimal(str(record.get("currentPeriodAmount", 0)))
                    revenue_map[year] += revenue
                except:
                    continue  # 忽略无效数据

    # 提取流动资产
    for record in data["data"]["financeBalance"]:  # 假设流动资产在 financeBalance 结构中
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "流动资产合计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初流动资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末流动资产

                    # 累加期初和期末流动资产，并增加计数
                    current_assets_map[year]["initial_sum"] += initial_balance
                    current_assets_map[year]["ending_sum"] += ending_balance
                    current_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    years = sorted(set(revenue_map.keys()).union(set(current_assets_map.keys())))

    # 用于记录风险年份及对应的流动资产周转率
    risk_years = {}
    INDUSTRY_STANDARD = Decimal("1.5")  # 行业标准：1.5次/年

    for year in years:
        # 获取营业收入
        revenue = revenue_map.get(year, Decimal("0"))

        # 获取期初和期末流动资产的总和及提取次数
        initial_sum = current_assets_map.get(year, {}).get("initial_sum", Decimal("0"))
        ending_sum = current_assets_map.get(year, {}).get("ending_sum", Decimal("0"))
        count = current_assets_map.get(year, {}).get("count", 0)

        # 计算平均流动资产
        average_current_assets = Decimal("0")
        if count > 0:
            average_current_assets = (initial_sum + ending_sum) / (2 * count)

        # 计算流动资产周转率
        turnover_ratio = Decimal("0")
        if average_current_assets != 0:
            turnover_ratio = revenue / average_current_assets

        # 计算流动资产周转天数
        turnover_days = Decimal("0")
        if turnover_ratio != 0:
            turnover_days = Decimal("365") / turnover_ratio
            turnover_days = Decimal(math.ceil(turnover_days))

        # 若流动资产周转率低于行业标准，则记录风险数据
        if turnover_ratio < INDUSTRY_STANDARD:
            risk_years[year] = float(turnover_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(float(revenue), 2),
                "平均流动资产（元）": round(float(average_current_assets), 2),
                "流动资产周转率": round(float(turnover_ratio), 2),
                "流动资产周转天数": int(turnover_days),
            }
        )

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的流动资产周转率为{round(ratio, 2)}，低于行业标准的1.5次/年，"
        risk_desc += (
            "流动资产周转率偏低或周转天数过长可能表明企业流动资产利用效率低下，或存在流动资产虚增、占用过多等问题，需重点关注流动资产的构成及变现能力。"
        )
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_total_assets_turnover(data):
    """
    计算总资产周转率和总资产周转天数，按年统计，并进行风险识别

    Args:
        data (dict): 包含资产负债表和利润表数据的字典

    Returns:
        tuple: (list, str) 包含每年所属期、营业收入、平均总资产、总资产周转率和总资产周转天数的字典列表，以及风险描述
    """
    revenue_map = defaultdict(Decimal)  # 存储每年的营业收入
    total_assets_map = defaultdict(
        lambda: {"initial_sum": Decimal("0"), "ending_sum": Decimal("0"), "count": 0}
    )  # 存储每年的期初和期末总资产总和及提取次数

    # 提取营业收入
    for record in data["data"]["financeProfit"]:
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "一、营业收入":
                try:
                    revenue = Decimal(str(record.get("currentPeriodAmount", 0)))
                    revenue_map[year] += revenue
                except:
                    continue  # 忽略无效数据

    # 提取总资产
    for record in data["data"]["financeBalance"]:  # 假设总资产在 financeBalance 结构中
        if record.get("levyProjectName") == "财务报表报送与信息采集（企业会计准则一般企业）":
            year = record.get("endDate")[:4]  # 从 endDate 中提取年份
            if record.get("projectName") == "资产总计":
                try:
                    initial_balance = Decimal(
                        str(record.get("initialBalance", 0))
                    )  # 期初总资产
                    ending_balance = Decimal(
                        str(record.get("endingBalance", 0))
                    )  # 期末总资产

                    # 累加期初和期末总资产，并增加计数
                    total_assets_map[year]["initial_sum"] += initial_balance
                    total_assets_map[year]["ending_sum"] += ending_balance
                    total_assets_map[year]["count"] += 1
                except:
                    continue  # 忽略无效数据

    result = []
    years = sorted(set(revenue_map.keys()).union(set(total_assets_map.keys())))

    # 用于风险识别：记录风险年份及其总资产周转率
    risk_years = {}
    INDUSTRY_STANDARD = Decimal("0.6")  # 行业标准：0.6次/年

    for year in years:
        # 获取营业收入
        revenue = revenue_map.get(year, Decimal("0"))

        # 获取期初和期末总资产的总和及提取次数
        initial_sum = total_assets_map.get(year, {}).get("initial_sum", Decimal("0"))
        ending_sum = total_assets_map.get(year, {}).get("ending_sum", Decimal("0"))
        count = total_assets_map.get(year, {}).get("count", 0)

        # 计算平均总资产
        average_total_assets = Decimal("0")
        if count > 0:
            average_total_assets = (initial_sum + ending_sum) / (2 * count)

        # 计算总资产周转率
        turnover_ratio = Decimal("0")
        if average_total_assets != 0:
            turnover_ratio = revenue / average_total_assets

        # 计算总资产周转天数
        turnover_days = Decimal("0")
        if turnover_ratio != 0:
            turnover_days = Decimal("365") / turnover_ratio
            turnover_days = Decimal(math.ceil(turnover_days))

        # 若总资产周转率低于行业标准，则记录风险数据
        if turnover_ratio < INDUSTRY_STANDARD:
            risk_years[year] = float(turnover_ratio)

        # 存储结果，保留两位小数
        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(float(revenue), 2),
                "平均总资产（元）": round(float(average_total_assets), 2),
                "总资产周转率": round(float(turnover_ratio), 2),
                "总资产周转天数": int(turnover_days),
            }
        )

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"
        risk_desc = "风险描述："
        for year, ratio in risk_years.items():
            risk_desc += f"{year}年的总资产周转率为{round(ratio, 2)}，低于行业标准的0.6次/年，"
        risk_desc += (
            "总资产周转率偏低可能反映企业资产利用效率不足，或存在资产闲置、低效配置等问题，这可能进一步影响企业的盈利能力，需关注资产管理和运营效率。"
        )
        return risk_desc

    risk_description = generate_risk_description(risk_years)

    return result, risk_description
