from pathlib import Path
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 模型路径配置
    OURYOLO_MODEL_PATH: str = "/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/best.pt"
    DOCLAYOUT_MODEL_PATH: str = "/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/doclayout_yolo_docstructbench_imgsz1024.pt"
    YOLO_TABLE_DET_MODEL_PATH: str = "/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/table_yolo.pt"
    BERT_MODEL_PATH: str = "/resources/nl2document_resources/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights"
    BERT_MODEL_NAME: str = "bert-base-chinese"
    BERT_WEIGHTS: str = "bert_best.pt"
    
    # 模型配置
    DEVICE: Optional[str] = None
    BERT_DROPOUT: float = 0.3
    
    # 标签映射
    LABEL2ID: dict = {
        "A": 0,
        "B": 1,
        "0": 0,
        "1": 1
    }
    
    class Config:
        env_file = ".env"
        
settings = Settings() 