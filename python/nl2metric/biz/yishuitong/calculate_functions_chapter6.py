def calculate_revenue_tax_elastic_modulus(data):
    """
    营业收入与企业所得税贡献变动检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、应纳所得税额、营业收入变动率、所得税贡献变动率、营业收入与企业所得税贡献变动系数的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "营业收入（元）": 0.00,
                    "实际应纳所得税额（元）": 0.00,
                    "营业收入变动率": "--",
                    "所得税贡献变动率": "--",
                    "营业收入与企业所得税贡献变动系数": "--"
                },
                ...
            ]
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "tax": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取应纳所得税额
            if record["projectName"] == "八、实际应纳所得税额（28+29-30）":
                tax = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "tax": tax}
                else:
                    annual_data[year]["tax"] = tax

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 营业收入变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_growth_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 应纳税额变动率
            if annual_data[prev_year]["tax"] != 0:
                annual_data[year]["tax_growth_rate"] = (
                    annual_data[year]["tax"] - annual_data[prev_year]["tax"]
                ) / annual_data[prev_year]["tax"]
            else:
                annual_data[year]["tax_growth_rate"] = None  # 上一年应纳税额为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["revenue_growth_rate"] = None
            annual_data[year]["tax_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["revenue_growth_rate"]
        tax_growth_rate = annual_data[year]["tax_growth_rate"]
        if (
            sales_growth_rate is not None
            and tax_growth_rate is not None
            and tax_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / tax_growth_rate
            ) * 100
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    risks = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        tax = annual_data[year]["tax"]
        revenue_growth_rate = annual_data[year]["revenue_growth_rate"]
        tax_growth_rate = annual_data[year]["tax_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < 0.8 or elastic_modulus > 1.2
        ):
            risks.append({"year": year, "elastic_modulus": elastic_modulus})

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        tax_growth_rate_formatted = (
            f"{round(tax_growth_rate * 100, 2)}%"
            if tax_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "实际应纳所得税额（元）": tax,
                "营业收入变动率": revenue_growth_rate_formatted,
                "所得税贡献变动率": tax_growth_rate_formatted,
                "营业收入与企业所得税贡献变动系数": elastic_modulus_formatted,
            }
        )

    risk_desc = f"风险描述: "
    if risks:
        for i in risks:
            risk_desc += f"{i['year']}年，弹性系数为{i['elastic_modulus']},"
        risk_desc += f"弹性系数异常指示企业营业收入变动与所得税贡献变动之间存在不匹配的情况，可能是企业利润操纵、税收筹划异常或经营状况发生重大变化的信号，需要进一步调查核实。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc


def calculate_revenue_profit_elastic_modulus(data):
    """
    营业收入与营业利润变动检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、营业利润、营业收入变动率、营业利润变动率、营业收入与营业利润变动系数的列表，格式如下：
            [
                {
                    "所属期": "2022",
                    "营业收入（元）": 77227.72,
                    "营业利润（元）": -4191800.79,
                    "营业收入变动率": "-67.43%",
                    "营业利润变动率": "18.34%",
                    "营业收入与营业利润变动系数": -367.6
                },
                ...
            ]
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和营业利润
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业利润
            if record["projectName"] == "二、营业利润（1-2-3-4-5-6-7+8+9）":
                profit = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 营业收入变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_growth_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 营业利润变动率
            if annual_data[prev_year]["profit"] != 0:
                annual_data[year]["profit_growth_rate"] = (
                    annual_data[year]["profit"] - annual_data[prev_year]["profit"]
                ) / annual_data[prev_year]["profit"]
            else:
                annual_data[year]["profit_growth_rate"] = None  # 上一年营业利润为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["revenue_growth_rate"] = None
            annual_data[year]["profit_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        if (
            sales_growth_rate is not None
            and profit_growth_rate is not None
            and profit_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / profit_growth_rate
            ) * 100
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    risks = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        profit = annual_data[year]["profit"]
        revenue_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < 0.8 or elastic_modulus > 1.2
        ):
            risks.append({"year": year, "elastic_modulus": round(elastic_modulus, 2)})

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        profit_growth_rate_formatted = (
            f"{round(profit_growth_rate * 100, 2)}%"
            if profit_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "营业利润（元）": profit,
                "营业收入变动率": revenue_growth_rate_formatted,
                "营业利润变动率": profit_growth_rate_formatted,
                "营业收入与营业利润变动系数": elastic_modulus_formatted,
            }
        )

    risk_desc = f"风险描述: "
    if risks:
        for i in risks:
            risk_desc += f"{i['year']}年，弹性系数为{i['elastic_modulus']},"
        risk_desc += f"营业收入与营业利润变动弹性系数异常，表明企业收入增长与利润增长不匹配，可能是成本控制出现问题、收入确认存在异常或进行利润操纵的信号，需要深入分析企业具体情况。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc


def calculate_gross_margin_detection(data):
    """
    毛利率变动检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、营业成本、毛利率、毛利率变动率的列表，格式如下：
            [
                {
                    "所属期": "2023",
                    "营业收入（元）": 1183235.59,
                    "营业成本（元）": 969318.33,
                    "毛利率": "18.08%",
                    "毛利率变动率": "-102.24%"
                },
                ...
            ]
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和营业成本
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "cost": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本（填写A102010\\102020\\103000）":
                cost = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "cost": cost}
                else:
                    annual_data[year]["cost"] = cost

    # 计算毛利率
    for year in annual_data:
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        annual_data[year]["gross_margin"] = (
            ((revenue - cost) / revenue * 100) if revenue > 0 else 0
        )

    # 按年份排序
    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算毛利率变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 毛利率变动率
            if annual_data[prev_year]["gross_margin"] != 0:
                annual_data[year]["gross_margin_growth_rate"] = (
                    annual_data[year]["gross_margin"]
                    - annual_data[prev_year]["gross_margin"]
                ) / annual_data[prev_year]["gross_margin"]
            else:
                annual_data[year][
                    "gross_margin_growth_rate"
                ] = None  # 上一年毛利率为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["gross_margin_growth_rate"] = None

    # 格式化输出
    result = []
    risks = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        gross_margin = annual_data[year]["gross_margin"]
        gross_margin_growth_rate = annual_data[year]["gross_margin_growth_rate"]

        if gross_margin is not None and gross_margin < 15:
            risks.append(
                {
                    "year": year,
                    "revenue": revenue,
                    "cost": cost,
                    "profit": revenue - cost,
                    "gross_margin": round(gross_margin, 2),
                }
            )

        # 格式化毛利率和毛利率变动率
        gross_margin_formatted = f"{round(gross_margin, 2)}%"  # 保留两位小数并添加百分号
        gross_margin_growth_rate_formatted = (
            f"{round(gross_margin_growth_rate * 100, 2)}%"
            if gross_margin_growth_rate is not None
            else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "营业成本（元）": cost,
                "毛利率": gross_margin_formatted,
                "毛利率变动率": gross_margin_growth_rate_formatted,
            }
        )

    risk_desc = f"风险描述: "
    if risks:
        risk_desc += f"近三年内，"
        for i in risks:
            risk_desc += f"{i['year']}年营业收入为{i['revenue']}元，营业成本为{i['cost']}元，营业毛利为{i['profit']}元，营业收入毛利率为{i['gross_margin']}%，"
        risk_desc += f"持续低于行业平均水平可能指示企业产品竞争力或成本控制存在问题。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc


def calculate_gross_margin(data):
    """
    毛利率过低疑点检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、营业成本、营业毛利、营业收入毛利率的列表，格式如下：
            [
                {
                    "所属期": "2023",
                    "营业收入（元）": 1183235.59,
                    "营业成本（元）": 969318.33,
                    "营业毛利": 213917.26000000013,
                    "营业收入毛利率": "18.08%"
                },
                ...
            ]
        str: 风险描述
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和营业成本
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "cost": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本（填写A102010\\102020\\103000）":
                cost = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "cost": cost}
                else:
                    annual_data[year]["cost"] = cost

    # 计算营业毛利和毛利率
    for year in annual_data:
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        annual_data[year]["gross_profit"] = revenue - cost
        annual_data[year]["gross_margin"] = (
            (annual_data[year]["gross_profit"] / revenue * 100) if revenue > 0 else 0
        )

    # 格式化输出
    result = []
    risk_desc = "风险描述: "
    previous_gross_margin = None
    has_risk = False  # 标记是否存在风险

    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        gross_profit = annual_data[year]["gross_profit"]
        gross_margin = annual_data[year]["gross_margin"]

        # 格式化毛利率
        gross_margin_formatted = f"{round(gross_margin, 2)}%"  # 保留两位小数并添加百分号

        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(revenue, 2),
                "营业成本（元）": round(cost, 2),
                "营业毛利": round(gross_profit, 2),
                "营业收入毛利率": gross_margin_formatted,
            }
        )

        # 检查毛利率变动幅度是否超过 ±10%
        if previous_gross_margin is not None and previous_gross_margin != 0:
            margin_change = gross_margin - previous_gross_margin
            if abs(margin_change) > 10:
                risk_desc += f"{year}年毛利率波动较大，变动幅度为{round(margin_change, 2)}%，"
                has_risk = True  # 标记存在风险

        previous_gross_margin = gross_margin

    # 根据是否有风险生成最终描述
    if has_risk:
        risk_desc += "毛利率波动可能是成本核算异常或收入结构变化引起，也可能反映企业存在经营异常或财务操纵的风险，需重点核实收入和成本的真实性。"
    else:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


def calculate_net_profit(current_date, data):
    """
    连续亏损检查
    参数:
        current_date
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年利润总额、所得税费用、净利润的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "利润总额（元）": -3457633.41,
                    "所得税费用（元）": 0.0,
                    "净利润（元）": -3457633.41
                },
                ...
            ]
        str: 风险描述，格式为“连续亏损可能意味着企业持续经营能力不足，存在财务数据异常或潜在税务避税风险，需关注是否有隐瞒收入、虚列成本或费用的行为。”
    """
    # financeProfit
    records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的利润总额和所得税费用
    annual_data = {}
    current_year = int(current_date[:4])
    start_year = current_year - 3

    # 遍历corporateIncome数据
    for record in records:
        # 提取年份
        year = record["endDate"][:4]  # 从endDate中提取年份
        if int(year) < start_year:
            continue

        # 提取利润总额
        if (
            record["projectName"] == "三、利润总额（亏损总额以“-”号填列）"
            and record["period"] == "Year"
        ):
            profit = float(record["currentYearAccumulativeAmount"] or 0)
            if year not in annual_data:
                annual_data[year] = {"profit": profit, "tax": 0}
            else:
                annual_data[year]["profit"] = profit

        # 提取所得税费用
        if record["projectName"] == "减：所得税费用" and record["period"] == "Year":
            tax = float(record["currentYearAccumulativeAmount"] or 0)
            if year not in annual_data:
                annual_data[year] = {"profit": 0, "tax": tax}
            else:
                annual_data[year]["tax"] = tax
    # 计算净利润
    for year in annual_data:
        annual_data[year]["net_profit"] = (
            annual_data[year]["profit"] - annual_data[year]["tax"]
        )

    # 格式化输出
    result = []
    consecutive_loss_years = []  # 存储连续亏损的年份
    previous_year = None

    for year in sorted(annual_data.keys()):
        profit = annual_data[year]["profit"]
        tax = annual_data[year]["tax"]
        net_profit = annual_data[year]["net_profit"]

        result.append(
            {"所属期": year, "利润总额（元）": profit, "所得税费用（元）": tax, "净利润（元）": net_profit}
        )

        # 检查是否为连续亏损
        if net_profit < 0:
            if previous_year is not None and int(year) == int(previous_year) + 1:
                consecutive_loss_years.append(year)
            else:
                consecutive_loss_years = [year]  # 重置连续亏损年份
            previous_year = year
        else:
            consecutive_loss_years = []  # 重置连续亏损年份
            previous_year = None

    # 生成风险描述
    risk_desc = ""
    if len(consecutive_loss_years) >= 3:
        risk_desc = "风险描述: 近三年内，企业连续亏损（"
        risk_desc += "、".join(consecutive_loss_years)
        risk_desc += "）。连续亏损可能意味着企业持续经营能力不足，存在财务数据异常或潜在税务避税风险，需关注是否有隐瞒收入、虚列成本或费用的行为。"
    else:
        risk_desc = "风险描述：该指标项未检测到连续亏损风险"

    return result, risk_desc


def calculate_profit_detection(data):
    """
    盈利情况检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、营业成本、期间费用、期间费用率、营业毛利率、期间费用率与营业毛利率差值的列表，格式如下：
            [
                {
                    "所属期": "2023",
                    "营业收入（元）": 1183235.59,
                    "营业成本（元）": 969318.33,
                    "期间费用": 100,
                    "期间费用率": "0.01%",
                    "营业毛利率": "18.08%",
                    "期间费用率与营业毛利率差值": -18.07
                },
                ...
            ]
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入、营业成本和期间费用
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "cost": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本（填写A102010\\102020\\103000）":
                cost = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "cost": cost}
                else:
                    annual_data[year]["cost"] = cost

            # 提取管理费用
            if record["projectName"] == "减：管理费用（填写A104000）":
                expense_admin = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_admin"] = expense_admin
            # 提取销售费用
            if record["projectName"] == "减：销售费用（填写A104000）":
                expense_sales = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_sales"] = expense_sales
            # 提取财务费用
            if record["projectName"] == "减：财务费用（填写A104000）":
                expense_finance = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_finance"] = expense_finance

    # 计算期间费用（管理费用、销售费用、财务费用）
    for year in annual_data:
        annual_data[year]["period_expense"] = (
            annual_data[year].get("expense_admin", 0)
            + annual_data[year].get("expense_sales", 0)
            + annual_data[year].get("expense_finance", 0)
        )

    # 计算毛利率、期间费用率及其差值
    for year in annual_data:
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        period_fees = annual_data[year]["period_expense"]

        # 计算营业毛利率
        annual_data[year]["gross_margin"] = (
            ((revenue - cost) / revenue * 100) if revenue > 0 else 0
        )

        # 计算期间费用率
        annual_data[year]["period_fees_ratio"] = (
            (period_fees / revenue * 100) if revenue > 0 else 0
        )

        # 计算期间费用率与营业毛利率差值
        annual_data[year]["diff"] = (
            annual_data[year]["period_fees_ratio"] - annual_data[year]["gross_margin"]
        )

    # 格式化输出
    result = []
    risks = []
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        period_fees = annual_data[year]["period_expense"]
        gross_margin = annual_data[year]["gross_margin"]
        period_fees_ratio = annual_data[year]["period_fees_ratio"]
        diff = annual_data[year]["diff"]

        if (
            diff is not None
            and period_fees_ratio != 0
            and diff / period_fees_ratio > 0.05
        ):
            risks.append(year)

        # 格式化毛利率和期间费用率
        gross_margin_formatted = f"{round(gross_margin, 2)}%"  # 保留两位小数并添加百分号
        period_fees_ratio_formatted = f"{round(period_fees_ratio, 2)}%"  # 保留两位小数并添加百分号

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "营业成本（元）": cost,
                "期间费用": period_fees,
                "期间费用率": period_fees_ratio_formatted,
                "营业毛利率": gross_margin_formatted,
                "期间费用率与营业毛利率差值": round(diff, 2),  # 保留两位小数
            }
        )

    risk_desc = ""
    if risks:
        risk_desc = "风险描述: "
        risk_desc += "、".join(risks)
        risk_desc += "年，期间费用率与营业毛利率差值大于5%，当期间费用率过高侵蚀毛利时，可能表明企业盈利能力不足或费用核算不准确，需关注期间费用是否存在虚增或成本费用分摊不合理的问题。"
    else:
        risk_desc = "风险描述：该指标项未检测到⻛险 "

    return result, risk_desc


def calculate_profit_comparison(data):
    """
    盈利情况检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、营业成本、营业利润、成本利润率、销售利润率的列表，格式如下：
            [
                {
                    "所属期": "2023",
                    "营业收入（元）": 1183235.59,
                    "营业成本（元）": 969318.33,
                    "营业利润（元）": -1454652.81,
                    "成本利润率": "-150.07%",
                    "销售利润率": "-122.94%"
                },
                ...
            ]
    """
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入、营业成本和营业利润
    annual_data = {}

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "cost": 0, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本（填写A102010\\102020\\103000）":
                cost = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "cost": cost, "profit": 0}
                else:
                    annual_data[year]["cost"] = cost

            # 提取营业利润
            if record["projectName"] == "二、营业利润（1-2-3-4-5-6-7+8+9）":
                profit = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "cost": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    # 计算成本利润率和销售利润率
    for year in annual_data:
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        profit = annual_data[year]["profit"]

        # 计算成本利润率
        annual_data[year]["cost_profit_ratio"] = (
            (profit / cost * 100) if cost > 0 else 0
        )

        # 计算销售利润率
        annual_data[year]["revenue_profit_ratio"] = (
            (profit / revenue * 100) if revenue > 0 else 0
        )

    # 格式化输出
    result = []
    risk_cost = []
    risk_sale = []
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        cost = annual_data[year]["cost"]
        profit = annual_data[year]["profit"]
        cost_profit_ratio = annual_data[year]["cost_profit_ratio"]
        revenue_profit_ratio = annual_data[year]["revenue_profit_ratio"]

        if cost_profit_ratio < 5:
            risk_cost.append(year)
        if revenue_profit_ratio < 3:
            risk_sale.append(year)

        # 格式化成本利润率和销售利润率
        cost_profit_ratio_formatted = f"{round(cost_profit_ratio, 2)}%"  # 保留两位小数并添加百分号
        revenue_profit_ratio_formatted = (
            f"{round(revenue_profit_ratio, 2)}%"  # 保留两位小数并添加百分号
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "营业成本（元）": cost,
                "营业利润（元）": profit,
                "成本利润率": cost_profit_ratio_formatted,
                "销售利润率": revenue_profit_ratio_formatted,
            }
        )

    risk_desc = "风险描述: "
    if risk_cost:
        risk_desc += "、".join(risk_cost)
        risk_desc += "年，成本利润率低于5%, "
    if risk_sale:
        risk_desc += "、".join(risk_sale)
        risk_desc += "年，销售利润率低于3%, "
    risk_desc += "利润率异常偏低可能反映企业成本核算或收入确认存在问题，需关注是否存在利润操纵、虚增成本或收入真实性风险。"

    if risk_cost is None and risk_sale is None:
        risk_desc = "风险描述：该指标项未检测到风险"

    return result, risk_desc


def calculate_invoice_statistics_by_year(data):
    """
    按年份统计接收发票和销售发票的份数及金额，并检测是否存在“有进无销”或“有进低销”的风险。

    Args:
        data (dict): 包含发票数据的字典

    Returns:
        list: 按年份统计的接收发票和销售发票的份数及金额的列表，格式为:
            [
                {
                    "所属年度": "2021",
                    "接收发票份数": 10,
                    "接收发票金额（元）": 1234.5,
                    "销售发票份数": 5,
                    "销售发票金额（元）": 5678.9
                },
                ...
            ]
        str: 风险描述
    """
    # 初始化结果字典
    result = {}

    # 遍历发票数据
    for item in data.get("data", {}).get("items", []):
        # 获取 belongMonth
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue

        # 提取年份
        year = belong_month[:4]

        # 如果年份不在结果字典中，初始化该年份的统计
        if year not in result:
            result[year] = {
                "receive_count": 0,
                "receive_amount": 0.0,
                "sales_count": 0,
                "sales_amount": 0.0,
            }

        # 判断是接收发票还是销售发票
        if item.get("sign") == "进项":
            result[year]["receive_count"] += 1
            result[year]["receive_amount"] += float(item.get("money", 0))
        elif item.get("sign") == "销项":
            result[year]["sales_count"] += 1
            result[year]["sales_amount"] += float(item.get("money", 0))

    # 格式化输出
    final_result = []
    risk_years = []  # 存储存在风险的年份

    for year in sorted(result.keys()):
        receive_count = result[year]["receive_count"]
        receive_amount = result[year]["receive_amount"]
        sales_count = result[year]["sales_count"]
        sales_amount = result[year]["sales_amount"]

        final_result.append(
            {
                "所属年度": year,
                "接收发票份数": receive_count,
                "接收发票金额（元）": round(receive_amount, 2),
                "销售发票份数": sales_count,
                "销售发票金额（元）": round(sales_amount, 2),
            }
        )

        # 风险识别：接收发票金额比销售发票金额多 30% 以上，或者接收发票份数比销售发票份数多 50% 以上
        if sales_amount > 0 and (receive_amount / sales_amount > 1.3):
            risk_years.append(
                {
                    "年份": year,
                    "风险类型": "金额异常",
                    "接收发票金额": receive_amount,
                    "销售发票金额": sales_amount,
                }
            )
        if sales_count > 0 and (receive_count / sales_count > 1.5):
            risk_years.append(
                {
                    "年份": year,
                    "风险类型": "份数异常",
                    "接收发票份数": receive_count,
                    "销售发票份数": sales_count,
                }
            )

    # 生成风险描述
    risk_desc = "风险描述："
    if risk_years:
        risk_desc += "企业存在“有进无销”或“有进低销”的情况，具体如下：\n"
        for risk in risk_years:
            if risk["风险类型"] == "金额异常":
                risk_desc += (
                    f"{risk['年份']}年，接收发票金额（{round(risk['接收发票金额'], 2)}元）较销售发票金额（{round(risk['销售发票金额'], 2)}元）"
                    f"多{round((risk['接收发票金额'] / risk['销售发票金额'] - 1) * 100, 2)}%，可能存在虚开发票、虚构成本或存货积压问题。\n"
                )
            elif risk["风险类型"] == "份数异常":
                risk_desc += (
                    f"{risk['年份']}年，接收发票份数（{risk['接收发票份数']}份）较销售发票份数（{risk['销售发票份数']}份）"
                    f"多{round((risk['接收发票份数'] / risk['销售发票份数'] - 1) * 100, 2)}%，可能存在虚开发票、虚构成本或存货积压问题。\n"
                )
        risk_desc += "需警惕潜在的偷税漏税行为及交易真实性问题。"
    else:
        risk_desc += "该指标项未检测到风险。"

    return final_result, risk_desc
