try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 模型路径配置
    ASK_DOC_OCR_PATH: str = ""
    TABLE_RECOGNITION_PIPELINE_PATH: str = "runs/detect/train39/weights/table_recognition_v2.yaml"
    TABLE_CELL_DET_MODEL_NAME: str = "RT-DETR-L_wired_table_cell_det"
    TABLE_CELL_DET_MODEL_DIR: str = ""
    
    # PaddleOCR配置
    PADDLE_OCR_LANG: str = "ch"
    PADDLE_OCR_USE_ANGLE_CLS: bool = True
    
    # 设备配置
    DEVICE: Optional[str] = None
    USE_GPU: bool = False
    GPU_ID: int = 0
    
    # 默认参数
    DEFAULT_THRESHOLD: float = 0.5
    DEFAULT_BATCH_SIZE: int = 1
    DEFAULT_CONFIDENCE: float = 0.5
    
    class Config:
        env_file = ".env"
        
settings = Settings()
