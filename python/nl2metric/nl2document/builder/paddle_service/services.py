from typing import List
import numpy as np
from paddleocr import PaddleOCR
from paddlex import create_pipeline, create_model

from config import settings
from models import OCRResult
from utils import get_ocrs, remove_punctuation, cal_rect_area, nums2str
from logging_config import get_logger

logger = get_logger(__name__)

class PaddleModelService:
    """PaddleOCR模型服务类，负责管理所有模型的加载和推理"""
    
    def __init__(self):
        self.device = self._get_device()
        self.paddle_model = None
        self.table_det_model = None
        self.table_cell_det_model = None
        
    def _get_device(self) -> str:
        """获取设备配置"""
        if settings.DEVICE:
            return settings.DEVICE
        return "cpu"
    
    async def load_models(self):
        """加载所有模型"""
        try:
            # 加载PaddleOCR模型
            if self.device == "cpu" or not settings.USE_GPU:
                self.paddle_model = PaddleOCR(
                    use_angle_cls=settings.PADDLE_OCR_USE_ANGLE_CLS,
                    lang=settings.PADDLE_OCR_LANG,
                    use_gpu=False
                )
            else:
                self.paddle_model = PaddleOCR(
                    use_angle_cls=settings.PADDLE_OCR_USE_ANGLE_CLS,
                    lang=settings.PADDLE_OCR_LANG,
                    use_gpu=True,
                    gpu_id=settings.GPU_ID
                )
            
            # 加载表格检测模型
            pipeline_path = f"{settings.ASK_DOC_OCR_PATH}{settings.TABLE_RECOGNITION_PIPELINE_PATH}"
            self.table_det_model = create_pipeline(
                pipeline=pipeline_path,
                device=self.device,
            )
            
            # 加载表格单元格检测模型
            self.table_cell_det_model = create_model(
                model_name=settings.TABLE_CELL_DET_MODEL_NAME,
                model_dir=settings.TABLE_CELL_DET_MODEL_DIR,
                device=self.device
            )
            
            logger.info("所有模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise RuntimeError("模型初始化失败")
    
    async def paddle_ocr_inference(self, image: np.ndarray) -> List[OCRResult]:
        """PaddleOCR推理"""
        logger.info(f"开始PaddleOCR推理，图像尺寸: {image.shape}")
        try:
            ocr_results = self.paddle_model.ocr(image, cls=True)[0]
            res = []

            if not ocr_results:
                logger.info("PaddleOCR未检测到任何文本")
                return res

            logger.info(f"PaddleOCR检测到 {len(ocr_results)} 个文本区域")

            for ocr in ocr_results:
                # 文本坐标、文本、置信度
                pos, (text, confidence) = ocr
                simple_text = remove_punctuation(text, True).lower()

                if not simple_text or cal_rect_area(pos[0] + pos[2]) <= 0:
                    continue

                res.append(OCRResult(
                    bbox=nums2str(map(int, pos[0] + pos[2])),
                    text=simple_text,
                    confidence=confidence,
                    origin_text=text,
                    font=int(abs(pos[2][1] - pos[0][1])),
                    label="text"
                ))

            logger.info(f"PaddleOCR推理完成，返回 {len(res)} 个有效结果")
            return res
        except Exception as e:
            logger.error(f"PaddleOCR推理失败: {str(e)}", exc_info=True)
            raise
    
    async def table_det_inference(self, image: np.ndarray) -> List[OCRResult]:
        """表格检测推理"""
        logger.info(f"开始表格检测推理，图像尺寸: {image.shape}")
        try:
            res = []
            det_res = self.table_det_model.predict(
                input=image,
                use_doc_orientation_classify=False,
                use_doc_unwarping=False
            )

            table_count = 0
            for table in det_res:
                table_count += len(table["table_res_list"])
                for dic in table["table_res_list"]:
                    ocrs: List[OCRResult] = get_ocrs(
                        texts=dic["table_ocr_pred"]["rec_texts"],
                        bboxs=dic["table_ocr_pred"]["rec_boxes"].tolist(),
                        confs=dic["table_ocr_pred"]["rec_scores"].tolist(),
                    )
                    res.extend(ocrs)

            logger.info(f"表格检测完成，检测到 {table_count} 个表格，返回 {len(res)} 个文本结果")
            return res
        except Exception as e:
            logger.error(f"表格检测推理失败: {str(e)}", exc_info=True)
            raise
    
    async def table_cell_det_inference(
        self,
        image: np.ndarray,
        threshold: float = None,
        batch_size: int = None
    ) -> List[OCRResult]:
        """表格单元格检测推理"""
        threshold = threshold or settings.DEFAULT_THRESHOLD
        batch_size = batch_size or settings.DEFAULT_BATCH_SIZE

        logger.info(f"开始表格单元格检测推理，图像尺寸: {image.shape}, 阈值: {threshold}, 批次大小: {batch_size}")
        try:
            res = []
            det_res = self.table_cell_det_model.predict(
                input=image,
                threshold=threshold,
                batch_size=batch_size
            )

            cell_count = 0
            for out in det_res:
                cell_count += len(out["boxes"])
                for dic in out["boxes"]:
                    x1, y1, x2, y2 = dic["coordinate"]
                    res.append(OCRResult(
                        bbox=nums2str([int(x1), int(y1), int(x2), int(y2)]),
                        text="",
                        confidence=round(dic["score"], 3),
                        origin_text="",
                        font=0,
                        label="cell"
                    ))

            logger.info(f"表格单元格检测完成，检测到 {cell_count} 个单元格")
            return res
        except Exception as e:
            logger.error(f"表格单元格检测推理失败: {str(e)}", exc_info=True)
            raise
