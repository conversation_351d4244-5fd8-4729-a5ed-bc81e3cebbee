import os
import tempfile
import traceback
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from typing import List, Dict, Optional, Tuple, Union

import aiosmtplib
import requests
import httpx
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError

from common.fs.fs import get_s3_file_system
from common.fs.storage import create_async_s3_client, create_s3_client
from common.logging.logger import get_logger
from common.utils.string_utils import class_to_dict
from nl2document.common.utils import save_to_pdf
from biz.yishuitong.report_generator import (
    generate_report,
    generate_html_report,
    save_html_to_pdf,
)
from biz.yishuitong.test.data import mock_report, Section, load_test_data
from biz.yishuitong.model import (
    CreateTaxReportRequest,
    TaxReport,
    ReportStatus,
    CheckReportStatus,
)
from sqlalchemy.orm import Mapped, mapped_column, Session, relationship, validates
from nl2document.common.models.model import engine, ONE_DAY_SECONDS, async_session
import asyncio
from sqlalchemy import select, update, or_, desc, and_
from pydantic import ValidationError

from nl2document.report_generate.service.service import ReportGenerateService


async def upload_to_s3(file_path: str, bucket: str, key: str):
    async with await create_async_s3_client() as s3:
        await s3.upload_file(file_path, bucket, key)


fapiao_url = "https://api.fundmol.com/query/fti/invoice/invoice-info"
tax_url = "https://api.fundmol.com/query/fti/invoice/finance-tax"

# def dict_data_to_markdown_table(section_data):
#     if section_data.data is None:
#         return ""
#     if not section_data.data:
#         return "| 状态 |\n| --- |\n| 未获取相应数据 |\n"
#
#     if all(isinstance(item, str) for item in section_data.data):
#         return f"""
# <table border="1">
#   <tr>
#     {"".join(f"<th>{col}</th>" for col in section_data.data)}
#   </tr>
#   <tr>
#     <td colspan="{len(section_data.data)}" style="text-align:center">未获取数据</td>
#   </tr>
# </table>
# """
#     # 提取列标题
#     headers = list(section_data.data[0].keys())
#
#     # 开始构建Markdown表格字符串
#     markdown_table = "| " + " | ".join(headers) + " |\n"
#
#     # 添加分隔线
#     markdown_table += "| " + " --- | " * len(headers) + "\n"
#
#     # 遍历数据，将每行数据添加到Markdown表格中
#     for row in section_data.data:
#         markdown_table += (
#             "| " + " | ".join([str(row[header]) for header in headers]) + " |\n"
#         )
#
#     return markdown_table


def dfs_collect_content(section: Section) -> List[str]:
    contents = []

    def dfs(section: Section, level: int):
        level_hash = "#" * level
        title = f"{level_hash} {section.title}\n"
        table = dict_data_to_markdown_table(section)
        image_markdown = ""
        if section.image_paths:
            for image_path in section.image_paths:
                image_markdown += f"![{section.title}](file://{image_path})\n\n"
        risk_desc = ""
        if section.risk_description:
            if section.has_risk:
                risk_desc = (
                    f'<p class="risk-warning">⚠️{section.risk_description}</p>\n\n'
                )
            else:
                risk_desc = f'<p class="risk-safe">✅{section.risk_description}</p>\n\n'
        content = (
            title
            + "\n\n"
            + (section.metric_description if section.metric_description else "")
            + "\n\n"
            + image_markdown
            + "\n\n"
            + table
            + "\n\n"
            + risk_desc
        )
        contents.append(content)
        # 如果有子节点，递归遍历每个子节点
        if section.subsections:
            for child in section.subsections:
                dfs(child, level + 1)

    # 遍历每个根节点，执行 DFS
    dfs(section, 1)

    return contents


def report_generation():
    report = mock_report().to_json()
    return report


class FetchDataAPIException(Exception):
    """API业务逻辑异常"""

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(f"[Code {code}] {message}")


async def fetch_data(req: CreateTaxReportRequest):
    app_key = os.environ.get("INVOICE_APP_KEY", "sdkjProd")
    headers = {"Content-Type": "application/json", "X-FM-APP-KEY": app_key}
    payload = {"taxNo": req.taxNo, "taxpayerId": req.taxpayerId}

    timeout = httpx.Timeout(timeout=5.0, read=600.0)
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            # 异步获取发票信息
            logger.info("开始获取发票信息")
            fapiao_response = await client.post(
                fapiao_url, json=payload, headers=headers
            )
            fapiao_response.raise_for_status()  # 处理HTTP错误
            fapiao_json = fapiao_response.json()

            if fapiao_json.get("code") != 200:
                raise FetchDataAPIException(
                    code=fapiao_json.get("code"),
                    message=fapiao_json.get("message", "未知发票API错误"),
                )
            logger.info("获取发票信息结束")

            # 异步获取财税信息（可以并行执行）
            logger.info("开始获取财税信息")
            tax_response = await client.post(tax_url, json=payload, headers=headers)
            tax_response.raise_for_status()
            tax_json = tax_response.json()

            if tax_json.get("code") != 200:
                raise FetchDataAPIException(
                    code=tax_json.get("code"),
                    message=tax_json.get("message", "未知财税API错误"),
                )
            logger.info("获取财税信息结束")

            return fapiao_json, tax_json

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP错误: {str(e)}")
            raise FetchDataAPIException(code=500, message=f"HTTP错误: {str(e)}")
        except httpx.RequestError as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise FetchDataAPIException(code=500, message=f"网络通信异常: {str(e)}")


async def async_send_email(receiver_email: str):
    # 邮箱配置

    smtp_server = os.environ.get("smtp_server", "smtp.feishu.cn")
    smtp_port = os.environ.get("smtp_port", "465")
    sender_email = os.environ.get("sender_email", "<EMAIL>")
    sender_password = os.environ.get("sender_password", "P9dA0XQVAJrrpoau")

    # 构建邮件内容
    msg = MIMEMultipart()
    msg["From"] = sender_email
    msg["To"] = receiver_email
    msg["Subject"] = "翼税通报告"
    body = "您好，您的翼税通报告已经生成完毕。"
    msg.attach(MIMEText(body, "plain"))

    # 异步发送
    try:
        async with aiosmtplib.SMTP(
            hostname=smtp_server, port=smtp_port, use_tls=True
        ) as server:
            await server.login(sender_email, sender_password)
            await server.send_message(msg)
        logger.info("邮件发送成功")
    except Exception as e:
        logger.error(f"发送失败: {e}")


async def generate_tax_report(request: Request, req: CreateTaxReportRequest):
    # if tax_data is None or fapiao_data is None:
    # fapiao_data, tax_data = load_test_data()
    try:
        fapiao_data, tax_data = await fetch_data(req)
    except FetchDataAPIException as e:
        async with async_session() as session:
            statement = select(TaxReport).where(TaxReport.order_id == req.orderId)
            result = await session.execute(statement)
            report = result.scalars().first()
            report.status = ReportStatus.ERROR.value
            report.message = e.message
            report.code = e.code
            await session.commit()
        logger.error(f"业务异常: 错误码={e.code}, 错误信息={e.message}")
        return
    except Exception as e:
        # 处理其他未预期的异常
        logger.error(f"系统异常: {str(e)}")
        return

    try:
        # report = TaxReport(report_id=report_id,
        #                    status = ReportStatus.COMPLETED.value,
        #                    sections = class_to_dict(mock_report()))
        with tempfile.TemporaryDirectory() as temp_dir:
            sections, summary_text, total_risk_num = await generate_report(
                fapiao_data, tax_data, temp_dir
            )
            logger.info("结束generate_report")
            results = await generate_html_report(sections, summary_text, total_risk_num)
            logger.info("结束dfs_collect_content")
            pdf_path = os.path.join(temp_dir, f"翼税通报告.pdf")
            remote_pdf_url = f"tax_report/order_{req.orderId}/翼税通报告.pdf"
            logger.info("开始save to pdf")
            await save_html_to_pdf(pdf_path, results, request)
            logger.info("结束save to pdf")
            logger.info(f"路径{pdf_path}")
            await upload_to_s3(pdf_path, "ask-doc", remote_pdf_url)
            logger.info("结束upload_file")
            remote_pdf_url = f"ask-doc/" + remote_pdf_url

            async with async_session() as session:
                statement = select(TaxReport).where(TaxReport.order_id == req.orderId)
                result = await session.execute(statement)
                report = result.scalars().first()
                report.status = ReportStatus.COMPLETED.value
                report.path = remote_pdf_url
                report.code = 0
                await session.commit()
                logger.info("结束写入数据库")

            if req.email is not None:
                await async_send_email(req.email)

            # async with  async_session() as session:
            #     stmt = select(TaxReport).where(TaxReport.report_id == report_id)
            #     result = await session.execute(stmt)
            logger.info(
                f"返回{get_s3_file_system().url(remote_pdf_url, expires=ONE_DAY_SECONDS * 365)}"
            )
            return get_s3_file_system().url(
                remote_pdf_url, expires=ONE_DAY_SECONDS * 365
            )
    except Exception as e:
        async with async_session() as session:
            statement = select(TaxReport).where(TaxReport.order_id == req.orderId)
            result = await session.execute(statement)
            report = result.scalars().first()
            report.status = ReportStatus.ERROR.value
            report.message = "生成报告失败：" + str(e)
            report.code = -1
            await session.commit()
            logger.error(f"生成报告失败: 错误信息={str(e)}")
            logger.error("Stack trace: %s", traceback.format_exc())
            return


logger = get_logger(__name__)


async def create_tax_report(request: Request, req: CreateTaxReportRequest):
    # with Session(engine) as session:
    #     new_report = TaxReport()
    #     updated_report = session.merge(new_report)
    #     session.commit()
    async with async_session() as session:
        # 先删除已存在的 order_id（可选）
        existing_report = await session.execute(
            select(TaxReport).where(TaxReport.order_id == req.orderId)
        )
        if existing_report := existing_report.scalar():
            await session.delete(existing_report)
            await session.commit()  # 提交删除操作

        # 新增记录
        new_report = TaxReport(order_id=req.orderId)
        session.add(new_report)
        await session.commit()  # 确保提交成功
        await session.refresh(new_report)  # 仅在对象处于 persistent 状态时调用

    task = asyncio.create_task(generate_tax_report(request, req))
    # remote_pdf_url = await generate_tax_report(
    #     new_report.report_id, req.tax_data, req.fapiao_data
    # )

    return new_report.order_id


def check_tax_report_status(req: CheckReportStatus):
    order_id = req.orderId
    try:
        with Session(engine) as session:
            report = (
                session.query(TaxReport).filter(TaxReport.order_id == order_id).first()
            )

            if not report:
                raise TaxReportNotFoundError(
                    f"Tax report not found for order_id: {order_id}"
                )

            # 转换枚举类型
            return report
    except SQLAlchemyError as e:
        raise DatabaseError(f"数据库操作失败: {str(e)}")


# 自定义异常类
class TaxReportNotFoundError(Exception):
    pass


class DatabaseError(Exception):
    pass


yishuitong_report_generate_router = APIRouter()


class YiShuiTongReportGenerateService(ReportGenerateService):
    def __init__(self):
        self.register_routes()

    def register_routes(self):
        yishuitong_report_generate_router.add_api_route(
            "/api/tax_report/create", self.create_tax_report, methods=["POST"]
        )

        yishuitong_report_generate_router.add_api_route(
            "/api/tax_report/report_status",
            self.check_tax_report_status,
            methods=["GET"],
        )

    async def create_tax_report(self, request: Request, req: CreateTaxReportRequest):
        try:
            report_id = await create_tax_report(request, req)
            return {"code": 0, "data": {"order_id": report_id}}
        except Exception as e:
            logger.error(f"create tax report failed: {e}")
            logger.error("Stack trace: %s", traceback.format_exc())

            return {
                "code": 500,
                "msg": str(e),
            }

    def check_tax_report_status(self, request: Request):
        query_params = request.query_params
        try:
            req = CheckReportStatus(**query_params)
        except ValidationError as e:
            return JSONResponse(status_code=400, content={"error": e.errors()})
        try:
            report = check_tax_report_status(req)
            if report.status == ReportStatus.COMPLETED.value:
                return {"code": 0, "data": {"order_id": req.orderId, "url": report.url}}
            elif report.status == ReportStatus.PENDING.value:
                return {"code": 202, "data": {"order_id": req.orderId}}
            else:
                return {"code": report.code, "data": {}, "message": report.message}
        except Exception as e:
            logger.error(f"check tax report status failed: {e}")
            logger.error("Stack trace: %s", traceback.format_exc())

            return {
                "code": 500,
                "msg": str(e),
            }


yishuitong_report_generate_service = YiShuiTongReportGenerateService()


if __name__ == "__main__":
    print(report_generation())
