from typing import List, Dict, Any, Set, Tuple, Optional, Union, Literal
from enum import Enum
from PIL import Image
import collections
from dataclasses import dataclass
import json
import re
import string


from common.logging.logger import get_logger

logger = get_logger(__name__)


class LabelType(Enum):
    # 共用标签
    TITLE = "title"
    TEXT = "text"
    FIGURE = "figure"
    TABLE = "table"
    # PPT特有标签
    HEADER_TITLE = "header_title"
    FLOW_CHART = "flow_chart"
    # pdf特有标签
    CATALOG = "catalog"
    # 未知标签
    UNKNOWN = "unknown"


class Statistics:
    """统计文本出现的次数, 然后进行过滤"""

    def __init__(
        self,
        contents: List[Dict[str, Any]],
        del_pic_under_char_nums: int,
        save_pic_beyond_char_num: int,
        ignores: List[str],
        **kwargs,
    ):
        self._contents = contents
        self._page = len(contents)
        self._del_pic_under_char_nums = del_pic_under_char_nums
        self._save_pic_beyond_char_num = save_pic_beyond_char_num
        self._ignores = ignores

    def _format(self, contents) -> List[Dict[str, str]]:
        res = []
        for cont in contents:
            res.append(
                {
                    "type": cont["type"],
                    "img_path": cont.get("img_path", ""),
                    "origin_content": cont["origin_content"],
                    "content": cont["content"],
                    "sha1": cont.get("sha1", ""),
                }
            )
        return res

    def useless_content(self) -> Set[str]:
        text_dic, filters = collections.defaultdict(list), set()
        _print = set()
        for cont in self._format([_c for c in self._contents for _c in c]):
            if cont["type"] == "text":
                if cont["origin_content"] in self._ignores:
                    filters.add(cont["origin_content"])
                    _print.add(cont["origin_content"])
                else:
                    text_dic[cont["content"]].append(cont)
            elif cont["type"] == "table":
                text_dic[cont["content"]].append(cont)
            elif cont["type"] == "figure":
                if (
                    self._del_pic_under_char_nums
                    <= len(cont["content"])
                    < self._save_pic_beyond_char_num
                ):
                    text_dic[cont["content"]].append(cont)
                elif len(cont["content"]) >= self._save_pic_beyond_char_num:
                    filters.update([cont["img_path"], cont["sha1"]])
            else:
                raise RuntimeError(f"暂不支持`{cont['type']}`过滤")

        candidates = []
        for text, list_dic in text_dic.items():
            if not text.strip():  # 空串直接过滤
                candidates.extend(list_dic)
            # 如果文档的页数 3< page <= 5
            elif 3 < self._page <= 5 and len(list_dic) >= self._page:
                candidates.extend(list_dic)
            # 如果文档的页数 5< page <=10
            elif 5 < self._page <= 10 and len(list_dic) >= self._page * 0.5:
                candidates.extend(list_dic)
            # 如果文档的页数 10< page <=20
            elif 10 < self._page <= 20 and len(list_dic) >= self._page * 0.4:
                candidates.extend(list_dic)
            # 如果文档的页数 20< page <=40
            elif 20 < self._page <= 40 and len(list_dic) >= self._page * 0.3:
                candidates.extend(list_dic)
            # 如果文档的页数 40< page
            elif 40 < self._page and len(list_dic) >= self._page * 0.2:
                candidates.extend(list_dic)

        for cand in candidates:
            if cand["type"] == "text":
                filters.add(cand["origin_content"])
                _print.add(cand["origin_content"])
            elif cand["type"] == "table":
                filters.add(cand["content"])
                _print.add(cand["content"])
            elif cand["type"] == "figure":
                filters.update([cand["img_path"], cand["sha1"]])
                _print.add(cand["sha1"])

        if _print:
            logger.info(f"以下内容将被过滤: {_print}")

        return filters

    def useful_contents(self, filtered: Set[str]) -> List[List[Dict[str, Any]]]:
        res: List[List] = []
        for conts in self._contents:
            res.append([])
            for cont in conts:
                _type = cont["type"]
                if _type == "text" and cont["origin_content"] in filtered:
                    continue
                elif _type == "table" and cont["content"] in filtered:
                    continue
                elif _type == "figure" and cont["sha1"] in filtered:
                    continue

                res[-1].append(cont)

        return res


@dataclass
class Content:
    file_type: Literal["pptx", "pdf", "docx"]
    bbox: str
    content_type: Literal["text", "figure", "table"]
    origin_content: str
    content: str = ""
    img_path: str = ""
    font: int = 0
    bold: bool = False
    color: Literal[1, 2] = 1
    italic: bool = False
    hyperlink: str = ""
    """
    Args:
        bbox:
        content_type:
        origin_content:
        content:
        font:
        bold:
        color: 1表示黑色字体, 2表示红色字体
        italic: 是否斜体
        hyperlink:
    """

    def to_json(self) -> Dict[str, str]:
        return self.__dict__

    def __str__(self) -> str:
        return dict2str(self.to_json())


@dataclass
class OCR:
    bbox: str = None
    text: str = None
    font: str = None
    origin_text: str = None
    confidence: float = None
    """
    Args:
        bbox:
        text: 处理后的内容
        font:
        origin_text: OCR原始内容
        confidence: 置信度
    """

    def to_json(self) -> Dict[str, str]:
        return self.__dict__

    def __str__(self) -> str:
        return dict2str(self.to_json())


@dataclass
class Layout:
    bbox: str
    label: str
    conf: float
    """
    Args:
        bbox: x1,y1,x2,y2表示左上角和右下角的坐标
        label: 标签
        conf: 标签对应的置信度
    """

    def to_json(self) -> Dict[str, Any]:
        return self.__dict__

    def __str__(self) -> str:
        return dict2str(self.to_json())


class Align:
    def __init__(
        self,
        file_type: str = None,
        bbox: str = None,
        img_path: str = None,
        content: str = None,
        label: str = "unknown",
        conf: float = 0.0,
        bold: bool = False,
        font: int = 0,
        italic: bool = False,
        hyperlink: str = "",
        color: Literal[1, 2] = 1,
        **kwargs,
    ):
        """
        Args:
            file_type: 原始文件类型
            bbox:
            img_path: 文档转图片的路径
            content: 文本内容
            label: 标签
            conf: 角色置信度
            bold: 文本是否为黑体
            font: 文本字体大小
            italic: 是否斜体
            hyperlink: 超链接
            color: 1表示黑色, 2表示红色
        """
        self.file_type = file_type
        self.bbox = bbox
        self.img_path = img_path
        self.content = content
        self.label = label
        self.conf = conf
        self.bold = bold
        self.font = font
        self.italic = italic
        self.hyperlink = hyperlink
        self.color = color

    def to_json(self) -> Dict[str, Any]:
        return self.__dict__

    def __str__(self) -> str:
        return dict2str(self.to_json())


class EachPageContent:
    def __init__(
        self,
        file_type: str,
        bbox: Tuple[int, int, int, int],
        page: int,
        label: str,
        img_path: str = None,
        content: str = None,
        conf: float = None,
        bold: bool = None,
        font: int = None,
        italic: bool = None,
        hyperlink: str = None,
        color: Literal[1, 2] = None,
        sub_content: Optional[List["EachPageContent"]] = None,
        **kwargs,
    ):
        """
        Args:
            bbox: x1, y1, x2, y2分别表示文本框左上角和右下角的坐标点
            page: PPT或PDF对应的页码
            label: 当前内容属于哪个标签
            text: 文本内容
            size: 字体大小
            bold: 字体是否加粗
            italic: 是否斜体
            hyperlink: 是否有超链接
            color: 字体颜色
            sub_content: 嵌套文本会放在这里面
        """
        self.file_type = file_type
        self.bbox = bbox
        self.page = page
        self.img_path = img_path
        self.content = content
        self.label = label
        self.conf = conf
        self.bold = bold
        self.font = font
        self.italic = italic
        self.hyperlink = hyperlink
        self.color = color
        self.sub_content = sub_content if sub_content is not None else []

    def to_json(self) -> Dict[str, Any]:
        json_dict = self.__dict__.copy()
        if self.sub_content:
            json_dict["sub_content"] = [s_c.to_json() for s_c in self.sub_content]

        return json_dict

    def __getitem__(self, key):
        return getattr(self, key)


def get_img_height_width(img_path: str) -> Tuple[int, int]:
    """获取图片的高度和宽度"""
    with Image.open(img_path) as img:
        width, height = img.size

        return int(width), int(height)


def is_contain_chinese(text: str) -> bool:
    """判断字符串是否包含中文"""
    _match = re.search(r"[\u4e00-\u9fff]", text)
    return _match is not None


def is_contain_alpha(text: str) -> bool:
    """判断字符串是否包含字母"""
    _match = re.search(r"[a-zA-Z]", text)
    return _match is not None


# def remove_punctuation(text: str, remove_symbol: bool = False) -> str:
#     """
#     是否移除所有标点符号, 默认只删除字符串两端的标点符号

#     Args:
#         text
#         remove_symbol
#     """

#     def _is_use(char: str, remove_symbol: bool) -> bool:
#         ascii_letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
#         punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~¥%（）&……，。？～！@#¥%……&*（）——+-「」【】“”‘：；/《》〈〉"""
#         if "\u4e00" <= char <= "\u9fa5" or char.isdigit() or char in ascii_letters:
#             return True

#         if not remove_symbol and char in punctuation:
#             return True

#         return False

#     if remove_symbol:
#         texts = []
#         for char in text:
#             if not _is_use(char, True):
#                 continue
#             texts.append(char)


#         return "".join(texts)
#     else:
#         start, end = 0, len(text) - 1
#         for i, char in enumerate(text):
#             if _is_use(char, False):
#                 break
#             start += 1
#         for i in range(end, -1, -1):
#             if _is_use(text[i], False):
#                 end = i + 1
#                 break
#         return text[start:end]
def remove_punctuation(text: str, remove_symbol: bool = False) -> str:
    if not remove_symbol:
        return text

    printable = string.digits + string.ascii_letters
    texts = []
    for char in text:
        if "\u4e00" <= char <= "\u9fa5" or char in printable:
            texts.append(char)

    return "".join(texts)


def statistics_text_len(text: str) -> Tuple[List[str], str, int]:
    """统计字符串的长度"""
    texts: List[str] = []
    for char in text:
        if not texts:
            texts.append(char)
        # 连续的字母看成整体
        elif is_contain_alpha(texts[-1]) and is_contain_alpha(char):
            texts[-1] = texts[-1] + char
        # 连续的数字看成整体
        elif texts[-1].isdigit() and char.isdigit():
            texts[-1] = texts[-1] + char
        else:
            texts.append(char)

    return texts, text, len(texts)


def get_pos(
    text: Union[str, List[str]], sub_text: Union[str, List[str]]
) -> List[Tuple[int, int]]:
    """获取子串在父串对应的位置索引, 左闭右开原则"""
    if isinstance(text, str):
        text = list(text)
    if isinstance(sub_text, str):
        sub_text = list(sub_text)

    indices = []
    for i in range(len(text)):
        if sub_text == text[i : i + len(sub_text)]:
            indices.append((i, i + len(sub_text)))

    return indices


def nums2str(nums: Union[List, Tuple], is_float: bool = False) -> str:
    """
    将数字列表转成字符串

    Args:
        nums:
        is_float: 是否为浮点数, 默认为整数
    """
    if is_float:
        nums = [float(num) for num in nums]
    else:
        nums = [int(num) for num in nums]

    return str(nums)


def str2nums(string: str) -> Union[List[int], List[float]]:
    texts = [s for s in re.split(r"[^0-9.]+", string) if s]

    if texts[0].isdigit():
        nums = [int(d) for d in texts]
    else:
        nums = [float(d) for d in texts]

    return nums


def dict2str(dic: Dict[str, Any]) -> str:
    return json.dumps(dic, ensure_ascii=False)


def str2dict(dict_str: str) -> Dict[str, Any]:
    return json.loads(dict_str)


def is_rectangle_inside(
    rect1: Union[str, Tuple, List], rect2: Union[str, Tuple, List]
) -> str:
    """
    判断两个矩形为 包含、相交、相离
    Args:
        rect1: x1, y1, x2, y2 分别表示 左上角和右下角
        rect2: x3, y3, x4, y4 分别表示 左上角和右下角
    """
    if isinstance(rect1, str):
        rect1 = str2nums(rect1)
    if isinstance(rect2, str):
        rect2 = str2nums(rect2)

    (x1, y1, x2, y2), (x3, y3, x4, y4) = rect1, rect2

    # rect1包含rect2或rect1等于rect2
    if x1 <= x3 <= x4 <= x2 and y1 <= y3 <= y4 <= y2:
        return "内含"

    # rect2包含rect1或rect2等于rect2
    elif x3 <= x1 <= x2 <= x4 and y3 <= y1 <= y2 <= y4:
        return "内含"

    # 相交
    elif max(x1, x3) <= min(x2, x4) and max(y1, y3) <= min(y2, y4):
        return "相交"

    # 相离
    else:
        return "相离"


def cal_rect_area(point: Union[str, Tuple[int, int, int, int]]) -> float:
    """
    计算矩形的面积
    Args:
        x1,y1,x2,y2: 分别是矩形左上角和右下角的坐标
    """
    if isinstance(point, str):
        x1, y1, x2, y2 = str2nums(point)
    else:
        x1, y1, x2, y2 = point

    return (x2 - x1) * (y2 - y1)


def cal_mid_point(box: Union[str, List[int], Tuple[int]]) -> Tuple[int, int]:
    """
    计算文本框的中心点的坐标
    Args:
        box: x1, y1, x2, y2表示文本框左上角和右下角的坐标点
    """
    if isinstance(box, str):
        box = str2nums(box)

    x1, y1, x2, y2 = box
    mid_x = int((x1 + x2) / 2)
    mid_y = int((y1 + y2) / 2)

    return mid_x, mid_y


def cal_box(
    ocrs: Union[List[str], Set[Tuple], Set[str], List[OCR], List[Layout]],
) -> str:
    """计算文本框的box的大小"""
    min_x, min_y, max_x, max_y = float("inf"), float("inf"), 0, 0

    for ocr in ocrs:
        if isinstance(ocr, str):
            x1, y1, x2, y2 = str2nums(ocr)
        elif isinstance(ocr, tuple):
            x1, y1, x2, y2 = ocr
        else:
            x1, y1, x2, y2 = str2nums(ocr.bbox)

        min_x, min_y = min(min_x, x1), min(min_y, y1)
        max_x, max_y = max(max_x, x2), max(max_y, y2)

    return nums2str([min_x, min_y, max_x, max_y])


def judge_pos(layout: Layout, ocr: Union[Layout, OCR]) -> bool:
    """判断OCR中心点是否在Layout里面"""
    x1, y1, x2, y2 = str2nums(layout.bbox)
    mid_x, mid_y = cal_mid_point(ocr.bbox)

    judge = x1 <= mid_x <= x2 and y1 <= mid_y <= y2
    if not judge:
        return False

    return True


# if __name__=="__main__":
#     s=remove_punctuation('[的地方]')
#     print(s)
#     print()
