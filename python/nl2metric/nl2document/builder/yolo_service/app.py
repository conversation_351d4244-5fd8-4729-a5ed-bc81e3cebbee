from fastapi import FastAPI, HTTPException, Depends
from models import BertRequest, BertResponse, ImageRequest, ImageResponse, DetectionResult
from services import ModelService
from utils import base64_to_numpy
import logging
import sys
from typing import List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="YOLO图像检测服务")
model_service = ModelService()

async def get_model_service() -> ModelService:
    return model_service

@app.on_event("startup")
async def startup_event():
    """服务启动时加载模型"""
    await model_service.load_models()

@app.post("/yolo-ocr/inference", response_model=ImageResponse)
async def yolo_ocr_inference(
    request: ImageRequest,
    service: ModelService = Depends(get_model_service)
) -> ImageResponse:
    """处理图片的API endpoint
    
    Args:
        request: 包含base64编码图片的请求
        service: 模型服务实例
        
    Returns:
        ImageResponse: 检测结果
        
    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 将base64转换为numpy数组
        image_array = base64_to_numpy(request.image)
        
        # 使用YOLO模型进行推理
        results = await service.yolo_inference(
            image_array,
            request.model_type,
            request.conf
        )
        
        # 提取结果
        detections: List[DetectionResult] = []
        if results.boxes is not None:
            boxes = results.boxes.xyxy.cpu().numpy().astype(int).tolist()
            labels = results.boxes.cls.cpu().numpy().astype(int).tolist()
            confs = results.boxes.conf.cpu().numpy().tolist()
            
            for box, label_id, conf in zip(boxes, labels, confs):
                detections.append(
                    DetectionResult(
                        bbox=box,
                        label_id=label_id,
                        label=results.names[label_id],
                        confidence=conf
                    )
                )
    
        return ImageResponse(
            data={
                "detections": detections,
                "shape": image_array.shape
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理图片时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/bert/inference", response_model=BertResponse)
async def bert_inference(
    request: BertRequest,
    service: ModelService = Depends(get_model_service)
) -> BertResponse:
    """使用BERT模型进行文本推理
    
    Args:
        request: 包含文本的请求体
        service: 模型服务实例
        
    Returns:
        BertResponse: 处理后的文本响应
        
    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        result_text = await service.bert_inference(request.text)
        return BertResponse(
            data={
                "text": result_text
            }
        )
    except Exception as e:
        logger.error(f"处理文本时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 