from pydantic import BaseModel, Field
from typing import List, Dict

class ImageRequest(BaseModel):
    image: str  # base64编码的图片字符串
    threshold: float = 0.5
    batch_size: int = 1

class OCRResult(BaseModel):
    bbox: str
    text: str
    font: int
    origin_text: str
    confidence: float
    label: str

class ImageResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: Dict = {
        "ocr_results": List[OCRResult],
    }