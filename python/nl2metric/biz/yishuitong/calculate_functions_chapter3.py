from datetime import datetime, timedelta

import pandas as pd
from dateutil.relativedelta import relativedelta


def convert_to_year_month(date_str):
    # 使用 datetime 解析 YYYYMM 格式的日期
    date = datetime.strptime(date_str, "%Y%m")

    # 格式化为“*年*月”
    return date.strftime("%Y年%m月")


# 3.1
def calculate_monthly_stats(current_date, data, threshold=0.5):
    """
    3.1 增值税发票（专票+普票）用量变动异常检查

    Args:
        current_date: 当前日期，格式为'YYYYMM'
        data: 包含发票数据的字典

    Returns:
        output: 包含每月统计数据的列表，list of list
        risk_desc: 风险描述，格式为''
    """
    # 将current_date转换为datetime对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化结果字典
    monthly_stats = {}
    risk_dict = {}

    # 生成最近12个月的月份列表
    for i in range(12):
        month = current - relativedelta(months=i)
        month_key = month.strftime("%Y%m")
        monthly_stats[month_key] = {"count": 0, "amount": 0, "change": 0}

    # 遍历发票数据
    for item in data["data"]["items"]:
        # 只统计进项发票
        if item["sign"] != "进项":
            continue
        if not item["invoiceTypeName"].__contains__("增值税"):
            continue

        belong_month = item["belongMonth"]

        # 检查是否在最近12个月内
        if belong_month in monthly_stats:
            monthly_stats[belong_month]["count"] += 1
            monthly_stats[belong_month]["amount"] += float(item["amount"])

    # 计算环比变化
    sorted_months = sorted(monthly_stats.keys())
    for i in range(1, len(sorted_months)):
        current_month = sorted_months[i]
        prev_month = sorted_months[i - 1]
        prev_amount = monthly_stats[prev_month]["count"]

        if prev_amount != 0:
            change = (monthly_stats[current_month]["count"] - prev_amount) / prev_amount
            if threshold and abs(change) > threshold:
                risk_dict[convert_to_year_month(current_month)] = change
        else:
            change = 0
        monthly_stats[current_month]["change"] = change

    # 对结果进行排序
    sorted_stats = dict(sorted(monthly_stats.items(), reverse=False))

    # 格式化金额，保留2位小数
    for month in sorted_stats:
        sorted_stats[month]["amount"] = round(sorted_stats[month]["amount"], 2)
    """
    return sorted_stats
    """
    output = [
        {
            "所属月份": index,
            "开票份数": entry["count"],
            "开票金额(元)": entry["amount"],
            "用票量变动率": str(round(entry["change"] * 100, 2)) + "%",
        }
        for index, entry in sorted_stats.items()
    ]
    risk_pos = map(
        lambda x: (f"""{x[0]}({str(round(x[1]*100, 2))+"%"})"""),
        filter(lambda x: x[1] > 0, risk_dict.items()),
    )
    risk_neg = map(
        lambda x: (f"""{x[0]}({str(round(x[1]*100, 2))+"%"})"""),
        filter(lambda x: x[1] < 0, risk_dict.items()),
    )
    if risk_dict:
        risk_desc = f"""风险描述: \n{'、'.join(risk_dict.keys())}的增值税专用发票用量出现异常波动，环比变动率均超过{str(threshold*100)+"%"}。其中，"""
        if risk_pos:
            risk_desc += f"""{'、'.join(risk_pos)}呈现正向波动，"""
        if risk_neg:
            risk_desc += f"""{'、'.join(risk_neg)}呈现负向波动，"""
        risk_desc += (
            f"可能暗示企业经营状况异常，例如虚开发票、隐匿收入、虚增成本等，可能导致少缴税款，面临税务稽查和处罚的风险。建议关注变动原因，核实业务真实性。"
        )
    else:
        risk_desc = """风险描述：该指标项未检测到⻛险"""
    return output, risk_desc


# 3.2
def calculate_top10_items(current_date, data):
    """
    3.2 前十大销售、采购品目不一致风险检查

    Args:
        current_date: 当前日期，格式为'YYYYMM'
        data: 包含发票数据的字典

    Returns:
        output: list of list
        risk_desc: 风险描述，格式为''
    """
    current = datetime.strptime(current_date, "%Y%m")
    start_date = current - relativedelta(months=11)

    # 初始化统计容器
    total_sales = defaultdict(lambda: {"money": 0.0, "taxMoney": 0.0})
    total_purchase = defaultdict(lambda: {"money": 0.0, "taxMoney": 0.0})

    # 数据处理逻辑（保持不变）
    for item in data.get("data", {}).get("items", []):
        belong_month_str = item.get("belongMonth", "")
        if not belong_month_str:
            continue
        try:
            belong_month = datetime.strptime(belong_month_str, "%Y%m")
        except ValueError:
            continue
        if belong_month < start_date:
            continue

        sign = item.get("sign", "").strip()
        if sign not in ["销项", "进项"]:
            continue

        for detail in item.get("details", []):
            item_title = detail.get("itemTitle", "").strip()
            if not item_title:
                continue

            try:
                money = float(detail.get("money", 0.0))
                tax_amount = float(detail.get("taxMoney", 0.0))
            except ValueError:
                continue

            if sign == "销项":
                total_sales[item_title]["money"] += money
                total_sales[item_title]["taxMoney"] += tax_amount
            elif sign == "进项":
                total_purchase[item_title]["money"] += money
                total_purchase[item_title]["taxMoney"] += tax_amount

    # 生成销售和采购Top10列表（补足10项）
    sales_top10 = sorted(
        total_sales.items(), key=lambda x: x[1]["money"], reverse=True
    )[:10]
    purchase_top10 = sorted(
        total_purchase.items(), key=lambda x: x[1]["money"], reverse=True
    )[:10]

    # 补足至10项
    sales_top10 += [("--", {"money": "--", "taxMoney": "--"})] * (10 - len(sales_top10))
    purchase_top10 += [("--", {"money": "--", "taxMoney": "--"})] * (
        10 - len(purchase_top10)
    )

    # 合并为指定格式
    merged_data = []
    for idx in range(10):
        sales_item = sales_top10[idx]
        purchase_item = purchase_top10[idx]

        row = {
            "序号": idx + 1,  # 序号
            "销售品目": sales_item[0] if sales_item[0] != "--" else "--",  # 销售品目
            "销售金额": round(sales_item[1]["money"], 2)
            if isinstance(sales_item[1]["money"], float)
            else "--",  # 销售金额
            "销售税额": round(sales_item[1]["taxMoney"], 2)
            if isinstance(sales_item[1]["taxMoney"], float)
            else "--",  # 销售税额
            "采购品目": purchase_item[0] if purchase_item[0] != "--" else "--",  # 采购品目
            "采购金额": round(purchase_item[1]["money"], 2)
            if isinstance(purchase_item[1]["money"], float)
            else "--",  # 采购金额
            "采购税额": round(purchase_item[1]["taxMoney"], 2)
            if isinstance(purchase_item[1]["taxMoney"], float)
            else "--",  # 采购税额
        }
        merged_data.append(row)

    # 风险判定逻辑
    sales_items = [item[0] for item in sales_top10[:10]]  # 前10销售品目
    purchase_items = [item[0] for item in purchase_top10[:10]]  # 前10采购品目

    # 去除无效项目（即去掉 '--'）
    valid_sales = [item for item in sales_items if item != "--"]
    valid_purchase = [item for item in purchase_items if item != "--"]

    # 计算不一致数量
    inconsistent_count = len(set(valid_sales) - set(valid_purchase)) + len(
        set(valid_purchase) - set(valid_sales)
    )

    # 判断风险
    if inconsistent_count > 7:
        risk_info = "风险描述：\n近12月前十大销售品目和采购品目有超过7项不一致，可能存在经营转型等风险。"
    else:
        risk_info = "风险描述：\n该指标项未检测到风险"

    return merged_data, risk_info


from itertools import groupby
from operator import itemgetter


# 3.3
def get_max_date_name(content, ID, name, dt):
    # 处理None，替换为默认值（如空字符串）以便排序
    content.sort(key=lambda x: x.get(ID, "") if x.get(ID) is not None else "")

    result = []
    result_dict = dict()

    # 按id分组（此时已处理None，可安全比较）
    for key, group in groupby(content, key=lambda x: x.get(ID)):
        # 在每个组中找到date最大的name
        max_date_item = max(group, key=lambda x: x.get(dt, ""))
        result.append(
            {"id": key, "name": max_date_item.get(name), "date": max_date_item.get(dt)}
        )
        result_dict[key] = max_date_item.get(name)
    return result_dict


def calculate_mutual_invoices(current_date, data, threshold=0.2):
    """
    3.3 检测近12个月中存在互开发票的企业清单，并输出主要公司及其金额。

    Args:
        current_date (str): 当前日期，格式为'YYYYMM'
        data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

    Returns:
        list: 包含主要公司及其金额的列表，每个元素为一个字典，包含以下键：
            - 序号: 序号
            - 主要公司纳税人识别号: 主要公司的纳税人识别号
            - 主要公司名称: 主要公司的名称
            - 开出金额（元）: 主要公司开给对方的金额
            - 开进金额（元）: 对方开给主要公司的金额
    """
    # 将current_date转换为datetime对象
    current = datetime.strptime(current_date, "%Y%m")

    # 计算近12个月的起始日期
    start_date = current - relativedelta(months=11)

    # 初始化字典来存储开票金额
    invoice_map = {}
    xiao_invoice_map, jin_invoice_map = dict(), dict()

    # 遍历发票数据
    content = data.get("data", {}).get("items", [])
    content_idinfo = [
        {
            "ID": item["sellerTaxIde"],
            "name": item["sellerName"],
            "dt": item["createDate"],
        }
        for item in content
    ]
    content_idinfo += [
        {"ID": item["buyerTaxIde"], "name": item["buyerName"], "dt": item["createDate"]}
        for item in content
    ]
    id_dict = get_max_date_name(content_idinfo, "ID", "name", "dt")
    for item in content:
        belong_month_str = item.get("belongMonth", "")
        if not belong_month_str:
            continue  # 跳过没有belongMonth的发票
        try:
            belong_month = datetime.strptime(belong_month_str, "%Y%m")
        except ValueError:
            continue
        if belong_month < start_date:
            continue  # 跳过不在近12个月范围内的发票

        sign = item.get("sign", "").strip()
        if sign not in ["销项", "进项"]:
            continue  # 只处理销项和进项

        main_tax_id = item.get("taxpayerId", "")
        # 获取纳税人识别号和名称
        kaichu_total = 0
        if sign == "销项":
            A_tax_id = item.get("sellerTaxIde", "")

            if A_tax_id != main_tax_id:
                # print(sign, A_tax_id, main_tax_id)
                continue
            A_name = item.get("sellerName", "")
            B_tax_id = item.get("buyerTaxIde", "")
            B_name = item.get("buyerName", "")
            amount = float(item.get("amount", 0.0))

            # 记录A开给B的金额
            pair = tuple([A_tax_id, B_tax_id])
            if pair not in xiao_invoice_map:
                xiao_invoice_map[pair] = {
                    "seller_name": A_name,
                    "seller_rax": A_tax_id,
                    "buyer_name": B_name,
                    "kaichu_money": 0.0,
                    "kaijin_money": 0.0,
                    "trans_cnt": 0,
                }
            xiao_invoice_map[pair]["kaichu_money"] += amount
            xiao_invoice_map[pair]["trans_cnt"] += 1
            kaichu_total += amount

            # print("seller_name", A_name)
        elif sign == "进项":
            A_tax_id = item.get("buyerTaxIde", "")
            A_name = item.get("buyerName", "")
            B_tax_id = item.get("sellerTaxIde", "")
            if A_tax_id != main_tax_id:
                # print(sign,A_tax_id, main_tax_id)
                continue
            B_name = item.get("sellerName", "")
            amount = float(item.get("amount", 0.0))
            # 记录B开给A的金额
            pair = tuple([A_tax_id, B_tax_id])
            if pair not in jin_invoice_map:
                jin_invoice_map[pair] = {
                    "seller_name": B_name,
                    "seller_rax": B_tax_id,
                    "buyer_name": A_name,
                    "kaichu_money": 0.0,  # 都是从seller的角度，所以都是对kaichu_money的计算。
                    "kaijin_money": 0.0,
                    "trans_cnt": 0,
                }
            jin_invoice_map[pair]["kaichu_money"] += amount
            jin_invoice_map[pair]["trans_cnt"] += 1
            # print("buyer_name:", A_name, "seller_name:",B_name)

    # 取互开组合
    mutual_pairs = []
    insert_pair = set(jin_invoice_map.keys()).intersection(set(xiao_invoice_map.keys()))
    index = 1
    mutual_pairs.append(
        {
            "序号": index,
            "纳税人识别号": main_tax_id,
            "纳税人名称": id_dict[main_tax_id],
            "开出金额（元）": round(kaichu_total, 2),
            "开进金额（元）": "--",
        }
    )

    risk_company = []
    for pair in insert_pair:
        index += 1
        jin_item = jin_invoice_map[pair]
        xiao_item = xiao_invoice_map[pair]
        # print(jin_item)
        # print(xiao_item)
        # 替补所有的kaijin_money
        jin_item["kaijin_money"], xiao_item["kaijin_money"] = (
            xiao_item["kaichu_money"],
            jin_item["kaichu_money"],
        )
        # 当企业作为购买方：
        mutual_pairs.append(
            {
                "序号": index,
                "纳税人识别号": jin_item["seller_rax"],
                "纳税人名称": jin_item["seller_name"],
                "开出金额（元）": "--",
                "开进金额（元）": round(jin_item["kaijin_money"], 2),
            }
        )

        if kaichu_total != 0 and jin_item["kaijin_money"] / kaichu_total > threshold:
            risk_company.append(
                f"{jin_item['seller_name']}({round(jin_item['kaijin_money'], 2)}元)"
            )
        # 当企业作为销售方：
        # index += 1
        # mutual_pairs.append({
        #     '序号': index,
        #     '纳税人识别号': xiao_item['seller_name'],
        #     '纳税人名称': xiao_item['seller_rax'],
        #     '开出金额（元）': round(xiao_item['kaichu_money'], 2),
        #     '开进金额（元）': round(xiao_item['kaijin_money'], 2)
        # })
    if risk_company:
        risk_desc = f"""风险描述: \n近12个月内，与{','.join(risk_company)}存在频繁的互开发票行为，可能涉嫌虚开发票、循环开票等违法行为，旨在虚增成本或虚列收入，存在较大的税务稽查风险。建议审查交易的真实性，避免与不具备真实业务往来的企业进行发票交易。"""
    else:
        risk_desc = "风险描述: \n该指标项未检测到风险"

    return mutual_pairs, risk_desc


# 3.4
def calculate_top10_customers(current_date, data):
    """
    3.4 销售发票top10客户

    Args:
        current_date (str): 当前日期，格式为'YYYYMM'
        data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

    Returns:
        list: 包含销售额排名前十的企业的列表，每个元素为一个字典，包含以下键：
            - 序号
            - 纳税人名称
            - 金额（元）
            - 占比
        str: 风险描述
    """
    # 将current_date转换为datetime对象
    current = datetime.strptime(current_date, "%Y%m")

    # 计算近12个月的起始日期
    start_date = current - relativedelta(months=11)

    # 初始化字典来存储每个客户的销售额
    customer_sales = defaultdict(float)
    total_sales = 0.0

    # 遍历发票数据
    for item in data.get("data", {}).get("items", []):
        belong_month_str = item.get("belongMonth", "")
        if not belong_month_str:
            continue  # 跳过没有belongMonth的发票
        try:
            belong_month = datetime.strptime(belong_month_str, "%Y%m")
        except ValueError:
            continue
        if belong_month < start_date:
            continue  # 跳过不在近12个月范围内的发票

        sign = item.get("sign", "").strip()
        if sign != "销项":
            continue  # 只处理销项发票

        # 获取纳税人名称和金额
        taxpayer_name = item.get("buyerName", "").strip()
        amount = float(item.get("amount", 0.0))

        # 累加销售额
        customer_sales[taxpayer_name] += amount
        total_sales += amount

    # 计算销售额排名前十的客户
    sorted_customers = sorted(customer_sales.items(), key=lambda x: x[1], reverse=True)[
        :10
    ]

    # 计算占比并构建结果
    top10_customers = []
    risk_info = []  # 记录风险信息

    for index, (name, amount) in enumerate(sorted_customers, start=1):
        percentage = (amount / total_sales) * 100 if total_sales > 0 else 0
        rounded_amount = round(amount, 2)
        rounded_percentage = round(percentage, 2)

        # 记录占比超过50%的企业
        if rounded_percentage > 50:
            risk_info.append((name, f"{rounded_amount}元", f"{rounded_percentage}%"))

        top10_customers.append(
            {
                "序号": index,
                "纳税人名称": name,
                "金额（元）": rounded_amount,
                "占比": f"{rounded_percentage}%",
            }
        )

    # 构造风险描述
    if risk_info:
        risk_desc = f"风险描述：\n在近12月销售额排名前十的企业中，有{len(risk_info)}家企业销售额占比超过50%："
        for info in risk_info:
            risk_desc += f"\n纳税人名称：{info[0]}，销售额：{info[1]}，占比：{info[2]}"
    else:
        risk_desc = "风险描述：\n该指标项未检测到风险"

    return top10_customers, risk_desc


from collections import defaultdict


# 3.5
def calculate_top10_products_by_year(current_date, data, threshold=0.8):
    """
    3.5 检测近四年劳务销售发票中交易金额排名前十的商品种类，并按年份分开标注。

    Args:
        current_date (str): 当前日期，格式为'YYYYMM'
        data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

    Returns:
        list: 包含近四年每年交易金额排名前十的商品种类的列表，每个元素为一个字典，格式为:
        [
            {
                '项目': '销售top1商品种类',
                '2024年度': '种类1',
                '2023年度': '种类2',
                '2022年度': '种类3',
                '2021年度': '种类4'
            },
            {
                '项目': '销售top2商品种类',
                '2024年度': '种类5',
                '2023年度': '种类6',
                '2022年度': '种类7',
                '2021年度': '种类8'
            },
            ...
        ]
    """
    # 将current_date转换为datetime对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化字典来存储每年的商品种类交易金额
    yearly_product_sales = defaultdict(lambda: defaultdict(float))

    # 遍历发票数据
    for item in data.get("data", {}).get("items", []):
        belong_month_str = item.get("belongMonth", "")
        if not belong_month_str:
            continue  # 跳过没有belongMonth的发票
        try:
            belong_month = datetime.strptime(belong_month_str, "%Y%m")
        except ValueError:
            continue

        # 计算年份
        year = belong_month.year

        # 检查是否在近四年范围内
        if (current - belong_month).days > 4 * 365:
            continue  # 跳过不在近四年范围内的发票

        contain_detail = item.get("containDetail", 0)
        if contain_detail == 1:
            continue  # 跳过containDetail为1的发票

        # 获取商品种类和交易金额
        for detail in item.get("details", []):
            product_name = detail.get("itemTitle", "")
            if not product_name:
                continue  # 跳过没有商品名的明细
            try:
                amount = float(detail.get("money", 0.0))
                product_name = product_name.strip()
            except ValueError:
                continue  # 跳过无法转换为数字的值

            # 累加交易金额
            yearly_product_sales[year][product_name] += amount

    # 获取近四年的年份列表
    years = sorted(yearly_product_sales.keys(), reverse=True)

    # 初始化一个字典来存储每个商品的四年数据
    yearly_top10_products = {year: [] for year in years}
    yearly_top10_products_rate = {year: [] for year in years}
    yearly_products_total = {year: 0 for year in years}
    # 填充每个商品的四年数据
    for year in years:
        sorted_products = sorted(
            yearly_product_sales[year].items(), key=lambda x: x[1], reverse=True
        )[:10]
        yearly_top10_products[year] = [
            product_name for product_name, _ in sorted_products
        ]
        yearly_top10_products_rate[year] = sum(
            [product_amount for _, product_amount in sorted_products]
        ) / sum(yearly_product_sales[year].values())
    # print("yearly_top10_products", yearly_top10_products)
    # print("yearly_top10_products_rate", yearly_top10_products_rate)
    if yearly_top10_products_rate[max(years)] > threshold:
        risk_desc = """风险描述:\n过度依赖少数客户，可能导致企业经营风险加大。此外，客户构成或交易金额的异常变动，可能暗示存在关联交易或虚开发票等问题，需要关注潜在的税务风险。"""
    else:
        risk_desc = """风险描述:\n该指标项未检测到⻛险"""
    # 构建结果
    top10_products = []
    for rank in range(1, 11):
        result_item = {"项目": f"销售top{rank}商品种类"}
        for year in years:
            if rank <= len(yearly_top10_products[year]):
                result_item[f"{year}年度"] = yearly_top10_products[year][rank - 1]
            else:
                result_item[f"{year}年度"] = "--"
        top10_products.append(result_item)

    return top10_products, risk_desc


# 3.6
def calculate_top10_suppliers(current_date, data, threshold=0.8):
    """
    3.6 检测近12个月供应商中采购额排名前十的企业的交易情况。

    Args:
        current_date (str): 当前日期，格式为'YYYYMM'
        data (dict): 包含发票数据的字典，结构应包含 'data' -> 'items' 列表

    Returns:
        list: 包含采购额排名前十的供应商的列表，每个元素为一个字典，包含以下键：
            - index: 排名序号
            - seller_name: 销方名称
            - total_amount: 总采购额
            - percentage: 采购额占比
    """
    # 将current_date转换为datetime对象
    current = datetime.strptime(current_date, "%Y%m")

    # 计算近12个月的起始日期
    start_date = current - relativedelta(months=11)
    # 初始化字典来存储每个供应商的采购额
    supplier_purchases = defaultdict(float)
    total_purchases = 0.0  # 总采购额

    # 遍历发票数据
    for item in data.get("data", {}).get("items", []):
        belong_month_str = item.get("belongMonth", "")
        if not belong_month_str:
            continue  # 跳过没有belongMonth的发票
        try:
            belong_month = datetime.strptime(belong_month_str, "%Y%m")
        except ValueError:
            continue
        if belong_month < start_date:
            continue  # 跳过不在近12个月范围内的发票

        sign = item.get("sign", "").strip()
        if sign != "进项":
            continue  # 只处理进项发票

        # 获取销方名称和采购额
        seller_name = item.get("sellerName", "").strip()
        amount = float(item.get("amount", 0.0))

        # 累加采购额
        supplier_purchases[seller_name] += amount
        total_purchases += amount

    # 计算采购额排名前十的供应商
    sorted_top10_suppliers = sorted(
        supplier_purchases.items(), key=lambda x: x[1], reverse=True
    )[:10]
    sorted_top10_suppliers_rate = sum(
        [item[1] for item in sorted_top10_suppliers]
    ) / sum(supplier_purchases.values())
    risk_desc = (
        "风险描述:\n过度依赖少数供应商，可能影响企业供应链稳定性。此外，供应商构成或交易金额的异常变动，可能暗示存在关联交易或虚开发票等问题，需要关注潜在的税务风险。"
        if sorted_top10_suppliers_rate > threshold
        else "风险描述:\n该指标项未检测到⻛险"
    )
    # print("sorted_top10_suppliers_rate", sorted_top10_suppliers_rate)
    # 构建结果，加入index
    top10_suppliers = [
        {
            "序号": index + 1,  # 排名序号从1开始
            "纳税人名称": name,
            "金额（元）": round(amount, 2),
            "占比": f"{round((amount / total_purchases) * 100, 2)}%"
            if total_purchases > 0
            else "0%",
        }
        for index, (name, amount) in enumerate(sorted_top10_suppliers)
    ]

    return top10_suppliers, risk_desc


# 3.7
def get_top10_procurement_items(current_date, data, threshold=5):
    """
    3.7 计算采购发票top10商品种类

    Args:
        current_date: 当前日期，格式为'YYYYMM'
        data: 包含发票数据的字典

    Returns:
        list: 包含近四年统计数据的列表，格式为:
        [
            {
                "项目": "采购top1商品种类",
                "2024年度": "*文化服务*服务费",
                "2023年度": "*文化服务*服务费",
                "2022年度": "*文化服务*服务费",
                "2021年度": "*文化服务*服务费"
            },
            ...
        ]
    """
    from datetime import datetime
    from collections import defaultdict

    current = datetime.strptime(current_date, "%Y%m")
    current_year = current.year
    years = [str(current_year - i) for i in range(4)]
    yearly_totals = {year: defaultdict(float) for year in years}

    # 遍历发票数据
    for item in data["data"]["items"]:
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue
        year = belong_month[:4]
        if year not in yearly_totals:
            continue
        for detail in item.get("details", []):
            item_title = detail.get("itemTitle", "")
            item_money = float(detail.get("money", 0))  # 注意这里原代码可能错误，应为detail的money
            yearly_totals[year][item_title] += item_money

    # 处理空年份并生成排名数据
    valid_years = []
    top10_set = {}
    year_ranks = {}
    top10_items = {}
    for year in years:
        items = yearly_totals[year]
        if not items:
            continue
        valid_years.append(year)
        sorted_items = sorted(items.items(), key=lambda x: x[1], reverse=True)[:10]
        top10_set[year] = {item[0] for item in sorted_items}
        # 生成商品到排名的映射
        year_ranks[year] = {
            item: rank + 1 for rank, (item, _) in enumerate(sorted_items)
        }
        # 填充top10_items
        for i, (item_title, _) in enumerate(sorted_items):
            key = f"top{i + 1}"
            if key not in top10_items:
                top10_items[key] = {}
            top10_items[key][year] = item_title

    # 构建结果表格
    result = []
    for i in range(1, 11):
        row = {"项目": f"采购top{i}商品种类"}
        for year in valid_years:
            row[f"{year}年度"] = top10_items.get(f"top{i}", {}).get(year, "--")
        # 补充缺失年份为"--"
        for y in years:
            if f"{y}年度" not in row:
                row[f"{y}年度"] = "--"
        result.append(row)

    # 风险检测逻辑
    risk_info = []
    sorted_years = sorted(valid_years, key=lambda x: int(x))
    for i in range(1, len(sorted_years)):
        year = sorted_years[i]
        prev_year = str(int(year) - 1)
        if prev_year not in sorted_years or sorted_years[i - 1] != prev_year:
            continue  # 确保连续年份

        # 条件1：集合对称差
        current_set = top10_set[year]
        prev_set = top10_set.get(prev_year, set())
        diff = current_set.symmetric_difference(prev_set)
        condition1 = len(diff) > threshold

        # 条件2：排名变动≥5
        condition2 = False
        current_rank_map = year_ranks.get(year, {})
        prev_rank_map = year_ranks.get(prev_year, {})

        # 检查当前年的商品
        for item in current_rank_map:
            prev_rank = prev_rank_map.get(item, 11)
            if abs(current_rank_map[item] - prev_rank) >= 5:
                condition2 = True
                break
        if not condition2:
            # 检查前一年的商品是否在当前年排名外
            for item in prev_rank_map:
                if item not in current_rank_map:
                    current_rank = 11
                    if abs(current_rank - prev_rank_map[item]) >= 5:
                        condition2 = True
                        break

        if condition1 or condition2:
            reasons = []
            if condition1:
                reasons.append(f"变动商品数量为{len(diff)}")
            if condition2:
                reasons.append("存在商品排名变动超过5名")
            risk_info.append((year, reasons))

    # 生成风险描述
    if risk_info:
        risk_descs = []
        for year, reasons in risk_info:
            desc = f"{year}年相比前一年" + "，".join(reasons)
            risk_descs.append(desc)
        risk_desc = (
            "风险描述:\n最近4年以来，采购商品种类变化较大。其中，"
            + "；".join(risk_descs)
            + "。这些变化可能反映了企业生产经营调整或市场变化。但如果与实际生产经营不符，则需要关注是否存在虚开发票或其他异常交易的风险。"
        )
    else:
        risk_desc = "风险描述:\n该指标项未检测到⻛险"

    return result, risk_desc


# 3.8
def calculate_voided_ratio(current_date, data, threshold1=0.05, threshold2=0.5):
    """
    3.8 计算近 12 个月的作废金额比例

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        tuple: 近 12 个月作废金额比例的列表和风险描述
    """
    current = datetime.strptime(current_date, "%Y%m")

    # 生成近 12 个月的月份列表
    months = [(current - relativedelta(months=i)).strftime("%Y%m") for i in range(12)][
        ::-1
    ]

    result = []
    risk_diff = {}
    risk_over = {}

    for month_str in months:
        voided_count = 0
        total_count = 0
        invalid_amount = 0.0
        total_amount = 0.0

        # 统计当前月数据
        for item in data["data"]["items"]:
            belong_month = item.get("belongMonth", "").replace("/", "")
            if belong_month != month_str:
                continue
            total_count += 1
            invalid_sign = item.get("invalidSign", "false")
            if invalid_sign == "true":
                voided_count += 1
                invalid_amount += float(item.get("money", 0))
            total_amount += float(item.get("money", 0))

        # 计算比例
        amount_ratio = invalid_amount / total_amount if total_amount else 0
        cnt_ratio = voided_count / total_count if total_count else 0

        # 风险条件1: 计算环比
        if len(result) > 0:
            res = result[-1]
            prev_avg = res["作废金额（元）"] / res["作废份数"] if res["作废份数"] else 0
            curr_avg = invalid_amount / voided_count if voided_count else 0
            monthly_avg = (curr_avg - prev_avg) / prev_avg if prev_avg else 0
            if abs(monthly_avg) > threshold2:
                risk_diff[month_str] = {
                    "作废比例": f"{round(cnt_ratio * 100, 2)}%",
                    "变动率": f"{round(monthly_avg * 100, 2)}%",
                }

        # 风险条件2: 比较阈值
        if cnt_ratio > threshold1:
            risk_over[month_str] = {"作废比例": f"{round(cnt_ratio * 100, 2)}%"}

        # 存储结果
        result.append(
            {
                "所属月份": f"{month_str[:4]}年{month_str[4:]}月",
                "作废份数": voided_count,
                "总份数": total_count,
                "作废金额（元）": invalid_amount,
                "作废比例": f"{round(cnt_ratio * 100, 2)}%" if cnt_ratio else "0.00%",
            }
        )

    # 生成风险描述
    risk_desc = "风险描述:\n"
    if risk_diff or risk_over:
        risk_desc += "连续12个月中，"
        # 输出环比异常
        if risk_diff:
            invalid_desc = ", ".join(
                [
                    f"{convert_to_year_month(key)}发票作废率{value['作废比例']}，环比变动{value['变动率']}"
                    for key, value in risk_diff.items()
                ]
            )
            risk_desc += f"{invalid_desc}。"
        # 输出阈值异常
        if risk_over:
            if risk_diff:
                risk_desc += "此外，"
            over_desc = ", ".join(
                [
                    f"{convert_to_year_month(key)}发票作废率{value['作废比例']}超过阈值{threshold1}"
                    for key, value in risk_over.items()
                ]
            )
            risk_desc += f"{over_desc}。"
        # 风险总结
        risk_desc += "这些异常暗示企业可能存在发票管理混乱、虚开发票或其他违规操作，增加税务稽查风险。建议加强发票管理，规范开票流程。"
    else:
        risk_desc = "该指标项未检测到⻛险"

    return result, risk_desc


# 3.9
def calculate_hc_ratio(current_date, data, threshold1=0.05, threshold2=0.5):
    """
    计算近 12 个月的红冲金额比例

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        list: 包含近 12 个月红冲金额比例的列表，格式为:
            [
                {
                    "所属月份": 202403,
                    "红冲份数": 2,
                    "总份数": 10,
                    "红冲金额（元）": 1234.5,
                    "红冲比例": "20%"
                },
                ...
            ]
    """
    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")
    # 初始化近 12 个月的月份列表
    months = [(current - relativedelta(months=i)).strftime("%Y%m") for i in range(12)]
    # 初始化结果列表
    result = []
    risk_diff = dict()
    risk_over = dict()

    # 遍历近 12 个月的月份
    for month in months[::-1]:
        # 初始化统计变量
        hc_count = 0  # 红冲份数
        total_count = 0  # 总份数
        hc_amount = 0.0  # 红冲金额
        total_amount = 0.0  # 总金额

        # 遍历发票数据
        for item in data["data"]["items"]:
            # 获取 belongMonth
            belong_month = item.get("belongMonth", "")
            if not belong_month:
                continue

            # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
            belong_month = belong_month.replace("/", "")

            # 如果 belongMonth 不是当前月份，跳过
            if belong_month != month:
                continue

            # 统计总份数
            total_count += 1

            # 判断是否作废
            hc_sign = item.get("hc", "false")

            if hc_sign == "是":
                hc_count += 1
                hc_amount += float(item.get("money", 0))
            total_amount += float(item.get("money", 0))

        # 计算作废比例
        amount_ratio = (hc_amount / total_amount) if total_amount > 0 else 0
        cnt_ratio = (hc_count / total_count) if total_count > 0 else 0

        # 风险条件1:计算环比
        if month > min(months):
            # prev_month = result[-1]["所属月份"]
            prev_voided_count = result[-1]["红冲份数"]
            prev_invalid_amount = result[-1]["红冲金额（元）"]
            pre_avg = (
                prev_invalid_amount / prev_voided_count if prev_voided_count else 0.0
            )
            current_avg = hc_amount / hc_count if hc_count else 0.0
            monthly_avg = (current_avg - pre_avg) / pre_avg if pre_avg else 0.0
            if abs(monthly_avg) > threshold2:
                risk_diff[month] = {
                    "红冲比例": f"{round(cnt_ratio*100, 2)}%"
                    if cnt_ratio is not None
                    else "0.00%",
                    "变动率": f"{round(monthly_avg*100, 2)}%"
                    if cnt_ratio is not None
                    else "0.00%",
                }
        # 风险条件2: 比较阈值
        if amount_ratio > threshold1:
            risk_over[month] = {
                "红冲比例": f"{round(cnt_ratio*100, 2)}%"
                if cnt_ratio is not None
                else "0.00%"
            }

        # 将结果存入列表
        result.append(
            {
                "所属月份": int(month),  # 转换为整数
                "红冲份数": hc_count,
                "总份数": total_count,
                "红冲金额（元）": round(hc_amount, 2),
                "红冲比例": f"{round(cnt_ratio*100, 2)}%"
                if cnt_ratio is not None
                else "0.00%",  # 保留一位小数并添加百分号
            }
        )
    risk_desc = "风险描述:\n"
    if risk_diff or risk_over:
        risk_desc += "连续12个月中，"
        if risk_diff:
            invalid_desc = ""
            for key, value in risk_diff.items():
                invalid_desc += f"""，{convert_to_year_month(key)}的发票红冲率是{value["红冲比例"]}，环比变动{value["变动率"]}"""
            risk_desc += f"""{'、'.join([convert_to_year_month(x) for x in risk_diff.keys()])}的发票红冲率出现了异常波动。其中{invalid_desc}。"""
        if risk_over:
            invalid_desc = "、".join(
                [
                    f"""{convert_to_year_month(x[0])}({x[1]["红冲比例"]})"""
                    for x in risk_over.items()
                ]
            )
            if risk_diff:
                risk_desc += "此外，"
            risk_desc += f"""{invalid_desc}的发票红冲率超过了预设阈值{threshold1}。"""
        risk_desc += f"""异常波动，可能反映企业存在交易错误、发票管理混乱或其他潜在问题，增加税务风险。建议加强内部控制，规范业务流程。"""
    else:
        risk_desc = "该指标项未检测到⻛险"
    return result, risk_desc


# 3.10
def calculate_zero_tax_ratio(current_date, data):
    """
    计算近 12 个月的零税额开票金额占比，按年份统计，并识别风险

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        tuple: 包含返回值：
            - list: 包含近 12 个月零税额开票金额占比的列表
            - str: 风险描述语句，例如：'风险描述: 该指标项未检测到风险' 或 '风险描述: 高比例的零税额发票可能存在风险...'
    """
    # 预设阈值
    THRESHOLD = 10  # 百分比，例如 10%

    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 获取近 12 个月的年份集合
    years = set()
    for i in range(12):
        month = current - timedelta(days=30 * i)
        years.add(month.year)

    # 初始化结果列表和风险信息
    result = []
    risk = {}  # 用于记录风险年份及其占比

    # 遍历每个年度
    for year in sorted(years):
        amount_zero = 0.0  # 零税额发票金额
        amount_non_zero = 0.0  # 非零税额发票金额

        # 遍历发票数据
        for item in data["data"]["items"]:
            belong_month = item.get("belongMonth", "")
            if not belong_month:
                continue

            # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
            belong_month = belong_month.replace("/", "")

            # 获取发票年份
            invoice_year = int(belong_month[:4])
            if invoice_year != year:
                continue

            # 获取发票金额
            invoice_amount = float(item.get("money", 0))
            tax_money = float(item.get("taxMoney", 0))
            if tax_money == 0:
                amount_zero += invoice_amount
            else:
                amount_non_zero += invoice_amount

        # 计算总金额
        total_amount = amount_zero + amount_non_zero

        # 检查总金额是否超过 10 万元
        if total_amount >= 100000:
            # 计算零税额开票金额占比
            ratio = (amount_zero / total_amount * 100) if total_amount > 0 else 0
            # 风险识别逻辑
            if ratio > THRESHOLD:
                risk[year] = round(ratio, 2)
        else:
            ratio = None  # 如果总金额未超过 10 万元，指标不生效

        # 保留两位小数并格式化百分比
        formatted_ratio = f"{round(ratio, 2)}%" if ratio is not None else "0.0%"

        # 添加到结果列表
        result.append(
            {
                "所属年度": year,
                "零税额开票金额（元）": round(amount_zero, 2),
                "非零税额开票金额（元）": round(amount_non_zero, 2),
                "零税额开票金额占比": formatted_ratio,
            }
        )

    # 生成风险描述语句
    if risk:
        risk_desc = "风险描述: 近12个月内，"
        risk_parts = [f"{year}年度（{ratio}%）" for year, ratio in risk.items()]
        risk_desc += "，".join(risk_parts) + f"取得税额为零的发票金额占比超过预设阈值（{THRESHOLD}%）。"
        risk_desc += " 大量取得税额为零的发票，可能与企业实际经营情况不符，可能存在虚开发票、取得不合规发票等风险，需要进一步核查。"
    else:
        risk_desc = "风险描述: 该指标项未检测到风险"

    return result, risk_desc


# 3.11
def calculate_outside_province_ratio(current_date, data):
    """
    计算近 12 个月的省外发票金额占比，并识别风险

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        tuple: 包含返回值：
            - list: 包含近 12 个月省外发票金额占比的列表
            - str: 风险描述语句，例如：'风险描述: 该指标项未检测到风险' 或 '风险描述: 高比例的省外发票可能存在风险...'
    """
    # 预设阈值
    THRESHOLD = 50  # 百分比，例如 50%

    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化近 12 个月的月份列表
    months = []
    for i in range(12):
        month = current - timedelta(days=30 * i)
        months.append(month.strftime("%Y%m"))

    # 获取涉及的年份
    years = set([month[:4] for month in months])  # 提取年份并去重

    # 初始化结果字典
    result = {
        year: {
            "local_count": 0,
            "local_amount": 0.0,
            "outside_count": 0,
            "outside_amount": 0.0,
            "ratio": None,
        }
        for year in years
    }

    # 遍历发票数据
    for item in data["data"]["items"]:
        # 获取进项销项信息
        sign = item.get("sign", "")
        if sign != "进项":
            continue

        # 获取 belongMonth
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue

        # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
        belong_month = belong_month.replace("/", "")

        # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
        if belong_month not in months:
            continue

        # 获取年份和月份
        year = belong_month[:4]
        month = belong_month[4:6]

        # 获取发票 ID
        invoice_id = item.get("id")
        if invoice_id is None:
            continue

        # 获取税号（统一社会信用代码）
        seller_id = item.get("sellerTaxIde", "")
        buyer_id = item.get("buyerTaxIde", "")

        # 提取行政区划码（第3位至第8位）
        def extract_province_code(tax_id):
            if len(tax_id) >= 8:  # 确保税号长度足够
                return tax_id[2:4]  # 提取第3位至第4位 表示省份
            return None

        seller_province_code = extract_province_code(seller_id)
        buyer_province_code = extract_province_code(buyer_id)

        # 判断是否省内
        is_local = True  # 默认认为是省内
        if seller_province_code and buyer_province_code:
            # 如果行政区划码相同，则为省内
            if seller_province_code == buyer_province_code:
                is_local = True
            else:
                is_local = False

        # 获取发票金额
        invoice_amount = float(item.get("money", 0))

        # 根据是否是省内累加份数和金额
        if is_local:
            result[year]["local_count"] += 1
            result[year]["local_amount"] += invoice_amount
        else:
            result[year]["outside_count"] += 1
            result[year]["outside_amount"] += invoice_amount

    # 计算每个年份的省外发票金额占比
    final_result = []
    risk = []  # 用于记录风险年份及其占比

    for year in sorted(result.keys()):
        total_amount = result[year]["local_amount"] + result[year]["outside_amount"]
        if total_amount >= 100000:  # 检查总金额是否超过 10 万元
            ratio = (
                (result[year]["outside_amount"] / total_amount * 100)
                if total_amount > 0
                else 0
            )
            # 风险识别逻辑
            if ratio > THRESHOLD:
                risk.append(f"{year}年{month}月（{round(ratio, 2)}%）")
        else:
            ratio = None  # 如果总金额未超过 10 万元，指标不生效

        # 保留两位小数并格式化百分比
        formatted_ratio = f"{round(ratio, 2)}%" if ratio is not None else "0.0%"

        # 添加到结果列表
        final_result.append(
            {
                "所属年度": year,
                "省内发票份数": result[year]["local_count"],
                "省内发票金额（元）": round(result[year]["local_amount"], 2),
                "省外发票份数": result[year]["outside_count"],
                "省外发票金额（元）": round(result[year]["outside_amount"], 2),
                "省外发票金额占比": formatted_ratio,
            }
        )

    # 生成风险描述语句
    if risk:
        risk_desc = (
            "风险描述: 近12个月内，"
            + "、".join(risk)
            + f"向省外企业开具发票金额占比超过预设阈值（{THRESHOLD}%），且与企业经营范围或客户群体不符。"
        )
        risk_desc += " 省外销售占比过高且不符合常理，可能存在虚开发票、转移利润等风险，需要关注交易的真实性和合理性。"
    else:
        risk_desc = "风险描述: 该指标项未检测到风险"

    return final_result, risk_desc


# 3.12
def calculate_proxy_invoice_ratio(current_date, data):
    """
    计算近 12 个月的接收代开发票金额占比，并识别风险

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        tuple: 包含返回值：
            - list: 近 12 个月接收代开发票金额占比的列表，格式为:
                [
                    {
                        "所属年度": "2021",
                        "非代开发票份数": 40,
                        "非代开发票金额（元）": 1234.5,
                        "接收代开发票份数": 10,
                        "接收代开发票金额（元）": 1234.5,
                        "接收代开发票金额占比": "50.1%"
                    },
                    ...
                ]
            - str: 风险描述语句，例如：'风险描述: 该指标项未检测到风险' 或 '风险描述: 高比例的代开发票可能存在风险...'
    """
    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化近 12 个月的月份列表
    months = []
    for i in range(12):
        month = current - timedelta(days=30 * i)
        months.append(month.strftime("%Y%m"))

    # 获取涉及的年份
    years = set([month[:4] for month in months])  # 提取年份并去重

    # 初始化结果字典
    result = {
        year: {
            "proxy_count": 0,
            "proxy_amount": 0.0,
            "non_proxy_count": 0,
            "non_proxy_amount": 0.0,
            "ratio": None,
        }
        for year in years
    }

    # 遍历发票数据
    for item in data["data"]["items"]:
        # 获取 belongMonth
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue

        # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
        belong_month = belong_month.replace("/", "")

        # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
        if belong_month not in months:
            continue

        # 获取年份
        year = belong_month[:4]

        # 获取发票 ID
        invoice_id = item.get("id")
        if invoice_id is None:
            continue

        # 判断是否是代开发票
        is_proxy = "代开" in str(item.get("remark", "")) or "代开" in str(
            item.get("sellerName", "")
        )

        # 获取发票金额
        invoice_amount = float(item.get("money", 0))

        # 根据是否是代开发票累加份数和金额
        if is_proxy:
            result[year]["proxy_count"] += 1
            result[year]["proxy_amount"] += invoice_amount
        else:
            result[year]["non_proxy_count"] += 1
            result[year]["non_proxy_amount"] += invoice_amount

    # 计算每个年份的接收代开发票金额占比
    final_result = []
    risk = {}  # 用于记录风险年份及其占比

    for year in sorted(result.keys()):
        total_amount = result[year]["proxy_amount"] + result[year]["non_proxy_amount"]
        if total_amount >= 100000:  # 检查总金额是否超过 10 万元
            ratio = (
                (result[year]["proxy_amount"] / total_amount * 100)
                if total_amount > 0
                else 0
            )
            # 风险识别逻辑
            if ratio > 10:  # 预设阈值为 10%
                risk[year] = round(ratio, 2)
        else:
            ratio = None  # 如果总金额未超过 10 万元，指标不生效

        final_result.append(
            {
                "所属年度": year,
                "非代开发票份数": result[year]["non_proxy_count"],
                "非代开发票金额（元）": round(result[year]["non_proxy_amount"], 2),
                "接收代开发票份数": result[year]["proxy_count"],
                "接收代开发票金额（元）": round(result[year]["proxy_amount"], 2),
                "接收代开发票金额占比": f"{round(ratio, 2)}%"
                if ratio is not None
                else "--",  # 保留一位小数并添加百分号
            }
        )

    # 生成风险描述语句
    if risk:
        risk_desc = "风险描述: 近12个月内，"
        risk_parts = [f"{year}年度（{ratio}%）" for year, ratio in risk.items()]
        risk_desc += "，".join(risk_parts) + "的收取代开发票金额占比超过阈值（10%）。"
        risk_desc += "高比例的代开发票可能暗示企业存在业务不合规、无法取得正规发票等问题，可能导致无法正常抵扣进项税额，增加税务风险。"
    else:
        risk_desc = "风险描述: 该指标项未检测到风险"

    return final_result, risk_desc


# 3.13
def service_fee_check(current_date, data):
    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化近 12 个月的月份列表
    months = []
    result = []
    total_amount = 0.0

    for i in range(12):
        month = current - timedelta(days=30 * i)
        months.append(month.strftime("%Y%m"))

    # 遍历发票数据
    for item in data["data"]["items"]:
        # 获取 belongMonth
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue

        # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
        belong_month = belong_month.replace("/", "")

        # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
        if belong_month not in months:
            continue

        # 获取年份
        year = belong_month[:4]

        # 检查服务类型
        details = item.get("details", [])
        if not details:
            continue

        item_title = str(details[0].get("itemTitle", ""))
        remark = str(item.get("remark", ""))

        # 匹配服务类型
        if "咨询服务" in item_title or "咨询服务" in remark:
            amount = float(item.get("money", 0.0))
            total_amount += amount
            result.append({"所属年度": year, "服务名称": item_title, "开票金额（元）": amount})

        elif "会议服务" in item_title or "会议服务" in remark:
            amount = float(item.get("money", 0.0))
            total_amount += amount
            result.append({"所属年度": year, "服务名称": item_title, "开票金额（元）": amount})

        elif "手续费" in item_title or "手续费" in remark:
            amount = float(item.get("money", 0.0))
            total_amount += amount
            result.append({"所属年度": year, "服务名称": item_title, "开票金额（元）": amount})

    # 风险判定
    risk_desc = "风险描述："
    if total_amount > 100000:  # 累计金额超过10万元
        risk_desc += "\n近12月累计开票金额超过10万元，服务类发票金额较大且缺乏佐证，容易被税务机关质疑其真实性，可能被认定为虚开发票，带来税务风险。建议加强合同管理，保留服务内容的证明材料。"
    else:
        risk_desc += "\n未检测到风险。"

    return result, risk_desc


# 3.14
def check_invoice_limit_risk(current_date, data):
    """
    检查发票限额风险，并返回风险发票信息和风险描述

    参数:
        current_date (str): 指定的年月，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    返回:
        tuple: 包含两个元素：
            - list: 风险发票信息列表
            - str: 风险描述语句
    """
    # 定义发票限额和阈值比例
    INVOICE_LIMITS = {
        "09c": 100000,  # 数电票（普通发票）
        "10": 100000,  # 增值税电子普通发票
        "04": 100000,  # 增值税普通发票
        "09s": 100000,  # 数电票（增值税专用发票）
        "11": 100000,  # 增值税普通发票（卷式）
    }
    THRESHOLD_RATIO = 0.95

    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 生成最近12个月的月份列表
    last_12_months = []
    for i in range(12):
        month = current - relativedelta(months=i)
        last_12_months.append(month.strftime("%Y-%m"))

    # 提取items数据
    items = data["data"]["items"]

    # 筛选近12个月数据
    recent_items = []
    for item in items:
        create_date = item["createDate"].split(" ")[0]  # 提取日期部分
        if create_date[:7] in last_12_months:
            recent_items.append(item)

    risk_invoices = []
    monthly_totals = {}  # 用于存储各月份的开票总金额

    for item in recent_items:
        inv_type = item["invType"]
        money = float(item["money"])
        create_date = item["createDate"].split(" ")[0]  # 提取日期部分
        month_key = create_date[:7]  # YYYY-MM格式

        # 记录各月份的开票总金额
        if month_key not in monthly_totals:
            monthly_totals[month_key] = 0.0
        monthly_totals[month_key] += money

        # 检查发票金额是否超过限额
        if inv_type in INVOICE_LIMITS:
            limit = INVOICE_LIMITS[inv_type]
            if money >= limit * THRESHOLD_RATIO:
                risk_invoices.append(
                    {
                        "发票状态": item["invoiceStatus"],
                        "开票日期": item["createDate"],
                        "发票种类": item["invoiceTypeName"],
                        "发票总金额（元）": money,
                    }
                )

    # 按开票日期升序排序
    risk_invoices = sorted(risk_invoices, key=lambda x: x["开票日期"])

    # 风险识别规则：单个月开票总金额超过30万元
    risky_months = []
    for month, total in monthly_totals.items():
        if total >= 300000:
            risky_months.append(f"{month}（{total}元）")

    # 生成风险描述语句
    risk_desc = "风险描述："
    if risky_months:
        risk_desc += f"近12个月内，以下月份的开票总金额超过30万元：{'、'.join(risky_months)}。"
        risk_desc += "频繁顶额开票可能为了逃避税务监管，例如为了避免开具专票而人为拆分交易，存在较大的税务风险。"
        risk_desc += "建议规范开票行为，避免人为拆分交易。"
    else:
        risk_desc += "该指标项未检测到风险"

    return risk_invoices, risk_desc


# 3.15
def analyze_monthly_invoice(currentDate, data, threshold=0.2):
    """
    按月份分析发票数据，并识别风险

    参数:
        currentDate (str): 指定的年月，格式为 'YYYYMM'
        data (Dict): 包含发票数据的字典

    返回:
        tuple: 包含返回值：
            - list: 按月份统计的发票金额和占比
            - str: 风险描述语句
    """

    # 将 currentDate 转换为 datetime 对象
    current = datetime.strptime(currentDate, "%Y%m")
    # 初始化结果字典
    monthly_stats = {}
    risk = {}  # 用于记录风险年份及其占比

    # 生成最近12个月的月份列表
    for i in range(12):
        month = current - relativedelta(months=i)
        month_key = month.strftime("%Y%m")
        monthly_stats[month_key] = {
            "individual_amount": 0.0,
            "non_individual_amount": 0.0,
            "ratio": None,
        }

    # 遍历发票数据
    for item in data["data"]["items"]:
        belong_month = item["belongMonth"].replace("/", "")  # 将 'YYYY/MM' 转换为 'YYYYMM'

        # 检查是否在最近12个月内
        if belong_month in monthly_stats:
            # 判断是否为个体户
            is_business_flag = False
            # 处理进项：检查sellerTaxIde是否存在且长度足够
            if (
                item["sign"] == "进项"
                and item.get("sellerTaxIde") is not None
                and len(item["sellerTaxIde"]) >= 2
                and item["sellerTaxIde"][1] == "2"
            ):
                is_business_flag = True
            # 处理销项：检查buyerTaxIde是否存在且长度足够
            elif (
                item["sign"] == "销项"
                and item.get("buyerTaxIde") is not None
                and len(item["buyerTaxIde"]) >= 2
                and item["buyerTaxIde"][1] == "2"
            ):
                is_business_flag = True

            # 累加金额
            money = float(item["money"])
            if is_business_flag:
                monthly_stats[belong_month]["individual_amount"] += money
            else:
                monthly_stats[belong_month]["non_individual_amount"] += money

    # 计算每个年份的个体户发票金额占比
    result = []
    for month, stats in monthly_stats.items():
        individual_amount = stats["individual_amount"]
        non_individual_amount = stats["non_individual_amount"]
        total_amount = individual_amount + non_individual_amount

        # 提取年份
        year = month[:4]
        ratio = (individual_amount / total_amount) if total_amount > 0 else 0
        # 风险识别逻辑
        print(threshold, ratio)
        if ratio > threshold:
            risk[year] = round(ratio * 100, 2)
        formatted_date = f"{year}年{month[4:6]}月"

        # 添加到结果列表
        result.append(
            {
                "所属月份": formatted_date,
                "个体户发票金额（元）": round(individual_amount, 2),
                "非个体户发票金额（元）": round(non_individual_amount, 2),
                "个体户发票金额占比": f"{round(ratio*100, 2)}%"
                if ratio is not None
                else "0.00%",
            }
        )

    # 按月份升序排序
    result = sorted(result, key=lambda x: x["所属月份"])

    # 生成风险描述语句

    if risk:
        risk_desc = "风险描述: 近12个月内，"
        risk_parts = [f"{year}年度（{ratio}%）" for year, ratio in risk.items()]
        risk_desc += (
            "，".join(risk_parts)
            + f"收取个体工商户发票金额占比超过预设阈值（{round(threshold*100, 2)}%），且交易金额较大。"
        )
        risk_desc += " 大量收取个体工商户发票，可能存在无法取得正规发票、交易不合规等风险，影响进项税额抵扣，增加税务风险。需要关注交易的真实性和发票的合规性，进一步核查发票来源和交易背景。"
    else:
        risk_desc = "风险描述: 该指标项未检测到风险"

    return result, risk_desc


# 3.16
from dateutil.relativedelta import relativedelta


def is_integer_amount(amount: float) -> bool:
    """判断金额是否为整数"""
    return amount.is_integer()


def check_large_amounts(current_date, data, threshold=20000):
    """检查大额整数发票"""

    """数据预处理"""
    data = data["data"]["items"]
    df = pd.DataFrame(data)

    # 日期转换
    df["createDate"] = pd.to_datetime(df["createDate"])
    # 金额转换为float
    df["money"] = pd.to_numeric(df["money"], errors="coerce")

    # 筛选近12个月数据
    current = datetime.strptime(current_date, "%Y%m")
    last_time = current - relativedelta(months=11)
    recent_df = df[df["createDate"] >= last_time]
    # 筛选条件：
    # 1. 进项发票
    # 2. 金额大于等于阈值
    # 3. 金额为整数
    filtered_df = recent_df[
        (recent_df["sign"] == "进项")
        & (recent_df["money"] >= threshold)
        & (recent_df["money"].apply(is_integer_amount))
    ]

    # 提取需要的字段
    result = []
    # invStatus
    # 0：正常
    # 1：作废
    # 2：红冲
    # 3：失控
    # 4：异常
    invStatus_dict = {"0": "正常", "01": "作废", "2": "红冲", "3": "失控", "4": "异常"}
    for _, row in filtered_df.iterrows():
        result.append(
            {
                "销售方名称": row["sellerName"],
                "发票状态": invStatus_dict[str(row["invStatus"])],
                "开票日期": row["createDate"].strftime("%Y-%m-%d %H:%M:%S"),
                "发票种类": row["invoiceTypeName"],
                "发票总金额": float(round(row["money"], 2)),
            }
        )
    if result:
        risk_desc = "风险描述:\n大额整数发票可能暗示人为操纵，可能存在虚开发票的风险，需要进一步核查发票的来源和真实性。"
    else:
        risk_desc = "⻛险描述:\n该指标项未检测到⻛险 "

    if not result:
        result = ["销售方名称", "发票状态", "开票日期", "发票种类", "发票总金额"]

    return result, risk_desc


# 3.17
def check_invalid_invoices(current_date, data, threshold=20000):
    """检查大额整数发票"""
    data = data["data"]["items"]
    df = pd.DataFrame(data)

    # 日期转换
    df["createDate"] = pd.to_datetime(df["createDate"])
    # 金额转换为float
    df["money"] = pd.to_numeric(df["money"], errors="coerce")

    # 筛选近12个月数据
    current = datetime.strptime(current_date, "%Y%m")
    last_time = current - relativedelta(months=11)
    recent_df = df[df["createDate"] >= last_time]
    # 筛选条件：
    # 1. 进项发票
    # 2. 金额大于等于阈值
    # 3. 金额为整数
    filtered_df = recent_df[
        (recent_df["sign"] == "进项")
        & (recent_df["money"] >= threshold)
        & (recent_df["invStatus"] == 1)
    ]

    # 提取需要的字段
    result = []
    # invStatus
    # 0：正常
    # 1：作废
    # 2：红冲
    # 3：失控
    # 4：异常
    invStatus_dict = {"0": "正常", "1": "作废", "2": "红冲", "3": "失控", "4": "异常"}
    for _, row in filtered_df.iterrows():
        result.append(
            {
                "销售方名称": row["sellerName"],
                "发票状态": invStatus_dict[str(row["invStatus"])],
                "开票日期": row["createDate"].strftime("%Y-%m-%d %H:%M:%S"),
                "发票种类": row["invoiceTypeName"],
                "发票总金额": float(round(row["money"], 2)),
            }
        )
    if result:
        risk_desc = "风险描述:\n大额进项作废发票可能反映交易存在重大问题，或存在虚开发票后作废以掩盖痕迹的可能，需要重点关注并核实原因。"
    else:
        risk_desc = "⻛险描述:\n该指标项未检测到⻛险 "

    if not result:
        result = ["销售方名称", "发票状态", "开票日期", "发票种类", "发票总金额"]
    return result, risk_desc


# 3.18
def calculate_receive_outside_province_ratio(current_date, data):
    """
    计算近 12 个月的接收省外发票金额占比，并识别风险

    Args:
        current_date (str): 当前日期，格式为 'YYYYMM'
        data (dict): 包含发票数据的字典

    Returns:
        tuple: 包含返回值：
            - list: 包含近 12 个月省外发票金额占比的列表
            - str: 风险描述语句，例如：'风险描述: 该指标项未检测到风险' 或 '风险描述: 高比例的省外发票可能存在风险...'
    """
    # 预设阈值
    THRESHOLD = 50  # 百分比，例如 50%

    # 将 current_date 转换为 datetime 对象
    current = datetime.strptime(current_date, "%Y%m")

    # 初始化近 12 个月的月份列表
    months = []
    for i in range(12):
        month = current - timedelta(days=30 * i)
        months.append(month.strftime("%Y%m"))

    # 获取涉及的年份
    years = set([month[:4] for month in months])  # 提取年份并去重

    # 初始化结果字典
    result = {
        year: {
            "local_count": 0,
            "local_amount": 0.0,
            "outside_count": 0,
            "outside_amount": 0.0,
            "ratio": None,
        }
        for year in years
    }

    # 遍历发票数据
    for item in data["data"]["items"]:
        # 获取进项销项信息
        sign = item.get("sign", "")
        if sign == "进项":
            continue

        # 获取 belongMonth
        belong_month = item.get("belongMonth", "")
        if not belong_month:
            continue

        # 将 'YYYY/MM' 格式的 belongMonth 转换为 'YYYYMM'
        belong_month = belong_month.replace("/", "")

        # 如果 belongMonth 不在近 12 个月的月份列表中，跳过
        if belong_month not in months:
            continue

        # 获取年份和月份
        year = belong_month[:4]
        month = belong_month[4:6]

        # 获取发票 ID
        invoice_id = item.get("id")
        if invoice_id is None:
            continue

        # 获取税号（统一社会信用代码）
        seller_id = item.get("sellerTaxIde", "")
        buyer_id = item.get("buyerTaxIde", "")

        # 提取行政区划码（第3位至第8位）
        def extract_province_code(tax_id):
            if tax_id is None:
                return None
            if len(tax_id) >= 8:  # 确保税号长度足够
                return tax_id[2:4]  # 提取第3位至第4位 表示省份
            return None

        seller_province_code = extract_province_code(seller_id)
        buyer_province_code = extract_province_code(buyer_id)

        # 判断是否省内
        is_local = True  # 默认认为是省内
        if seller_province_code and buyer_province_code:
            # 如果行政区划码相同，则为省内
            if seller_province_code == buyer_province_code:
                is_local = True
            else:
                is_local = False

        # 获取发票金额
        invoice_amount = float(item.get("money", 0))

        # 根据是否是省内累加份数和金额
        if is_local:
            result[year]["local_count"] += 1
            result[year]["local_amount"] += invoice_amount
        else:
            result[year]["outside_count"] += 1
            result[year]["outside_amount"] += invoice_amount

    # 计算每个年份的省外发票金额占比
    final_result = []
    risk = []  # 用于记录风险年份及其占比

    for year in sorted(result.keys()):
        total_amount = result[year]["local_amount"] + result[year]["outside_amount"]
        if total_amount >= 100000:  # 检查总金额是否超过 10 万元
            ratio = (
                (result[year]["outside_amount"] / total_amount * 100)
                if total_amount > 0
                else 0
            )
            # 风险识别逻辑
            if ratio > THRESHOLD:
                risk.append(f"{year}年{month}月（{round(ratio, 2)}%）")
        else:
            ratio = None  # 如果总金额未超过 10 万元，指标不生效

        # 保留两位小数并格式化百分比
        formatted_ratio = f"{round(ratio, 2)}%" if ratio is not None else "0.0%"

        # 添加到结果列表
        final_result.append(
            {
                "所属年度": year,
                "省内发票份数": result[year]["local_count"],
                "省内发票金额（元）": round(result[year]["local_amount"], 2),
                "省外发票份数": result[year]["outside_count"],
                "省外发票金额（元）": round(result[year]["outside_amount"], 2),
                "省外发票金额占比": formatted_ratio,
            }
        )

    # 生成风险描述语句
    if risk:
        risk_desc = (
            "风险描述: 近12个月内，" + "、".join(risk) + f"接收省外企业开具发票金额占比超过预设阈值（{THRESHOLD}%）。"
        )
        risk_desc += "省外进项占比过高且不符合常理，可能存在虚开发票、虚增成本等风险，需要关注交易的真实性和合理性。"
    else:
        risk_desc = "风险描述: 该指标项未检测到风险"

    return final_result, risk_desc
