import requests
from typing import List
from models import OCRResult
from utils import img2base64
def call_paddle_ocr_inference(image_path: str) -> List[OCRResult]:

    url = "http://localhost:8001/paddle-ocr/inference"
    image_base64, _ = img2base64(image_path)
    data = {
        "image": image_base64
    }
    response = requests.post(url, json=data)
    return response.json()

if __name__ == "__main__":
    image_path = "/test_image.png"
    result = call_paddle_ocr_inference(image_path)
    print(result)
