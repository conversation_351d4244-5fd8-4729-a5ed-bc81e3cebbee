from typing import List, Union, Tuple, Dict
from ultralytics import YOL<PERSON>
from doclayout_yolo import YOLOv10
from paddleocr import PaddleOCR
from paddlex import create_pipeline
import torch
import torch.nn as nn
import os
import logging
import string
import transformers
from transformers import BertTokenizer, BertModel

transformers.logging.set_verbosity_error()
from nl2document.builder.parser.utility import init_args
from nl2document.builder.parser.ppt_pdf_parser.utils import (
    OCR,
    Layout,
    remove_punctuation,
    nums2str,
    str2nums,
    cal_rect_area,
)
from config.doc_config import ASK_DOC_OCR_PATH, ASK_DOC_OCR_INFERENCE_PATH
from common.logging.logger import get_logger

logger = get_logger(__name__)


def silence_named_loggers(logger_names, level=logging.WARNING):
    for name in logger_names:
        logger = logging.getLogger(name)
        logger.setLevel(level)
        for handler in logger.handlers:
            handler.setLevel(level)


logging.getLogger("ppocr").setLevel(logging.INFO)
silence_named_loggers(["ultralytics", "doclayout_yolo"], level=logging.WARNING)


LAYOUT_CATEGORIES = {
    # ppt和pdf共用标签
    1: "title",
    2: "text",
    3: "figure",
    4: "table",
    # ppt特有标签
    5: "header_title",
    6: "flow_chart",
    # pdf特有标签
    7: "catalog",
}


def _cal_area_rate(
    bbox1: Union[str, Tuple[int]], bbox2: Union[str, Tuple[int]]
) -> float:
    """计算两个面积的比值"""
    if isinstance(bbox1, str):
        bbox1 = str2nums(bbox1)
    if isinstance(bbox2, str):
        bbox2 = str2nums(bbox2)

    x1_inter = max(bbox1[0], bbox2[0])
    y1_inter = max(bbox1[1], bbox2[1])
    x2_inter = min(bbox1[2], bbox2[2])
    y2_inter = min(bbox1[3], bbox2[3])

    # 交集宽高
    inter_width = max(0, x2_inter - x1_inter)
    inter_height = max(0, y2_inter - y1_inter)
    inter_area = inter_width * inter_height

    # 各框面积
    areaA = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
    areaB = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])

    return inter_area / min(areaA, areaB)


def _remove_cover_label(layouts: List[Dict], cover_rate: float = 0.8) -> List[Layout]:
    filtered = set()
    for i, l in enumerate(layouts):
        if str(l) in filtered:
            continue
        for j, _l in enumerate(layouts):
            if (
                i == j
                or str(_l) in filtered
                or _cal_area_rate(l["bbox"], _l["bbox"]) < cover_rate
            ):
                continue

            if l["label"] == "catalog" and _l["label"] in ("title", "text"):
                filtered.add(str(_l))
            if l["label"] == "catalog" and _l["label"] == "table":
                layout = _l if l["conf"] > _l["conf"] else l
                filtered.add(str(layout))
            if l["label"] == "table" and _l["label"] == "figure":
                filtered.add(str(l))

    res = [
        Layout(l["bbox"], l["label"], l["conf"])
        for l in layouts
        if str(l) not in filtered
    ]

    return res


class MyBertModel(nn.Module):
    def __init__(self, pretrain_model_url, num_labels, dropout, **kwargs):
        super().__init__(**kwargs)

        self.bert = BertModel.from_pretrained(pretrain_model_url)
        config = self.bert.config
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(config.hidden_size, num_labels)

    def forward(self, ids_inputs, attention_mask):
        # (batch, sentence_len, dim)
        hidden_state = self.bert(ids_inputs, attention_mask=attention_mask)[0]
        # 随机丢弃一部分
        last_hidden_states = self.dropout(hidden_state)
        logits = self.classifier(last_hidden_states[:, 0, :])
        return logits


def get_device(device: str = None):
    if device == "cpu":
        return torch.device("cpu")

    elif device.startswith("gpu:"):
        _, gpu_id = device.split(":")
        return torch.device(f"cuda:{gpu_id}")

    else:
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")


def get_pos(text: str):
    spans, texts = [], text.split("\n")
    for i in range(1, len(texts)):
        spans.append(["\n".join(texts[:i]), "\n".join(texts[i:])])

    return spans


def convert_to_id(tokenizer: BertTokenizer, text: str):
    input_ids = []
    for span in get_pos(text):
        tokens = tokenizer.tokenize(f"[CLS]{span[0]}[SEP]{span[1]}[SEP]")
        input_ids.append(tokenizer.convert_tokens_to_ids(tokens))

    return input_ids, max([len(ii) for ii in input_ids])


def _format_text(texts: List[str], label: str) -> str:
    res = ""
    for i, sub_text in enumerate(texts):
        if i == 0 or (i > 0 and label[i - 1] == "A"):
            res += sub_text
        else:
            res += f"\n{sub_text}"

    return res.strip()


def statistics_text_len(text: str) -> int:
    """统计字符串的长度"""
    texts: List[str] = []
    for char in text:
        if not texts:
            texts.append(char)
        # 连续的字母看成整体
        elif texts[-1] in string.ascii_letters and char in string.ascii_letters:
            texts[-1] = texts[-1] + char
        # 连续的数字看成整体
        elif texts[-1].isdigit() and char.isdigit():
            texts[-1] = texts[-1] + char
        else:
            texts.append(char)

    return len(texts)


class ParseModel:
    def __init__(self, language: str = "ch"):
        args = init_args().parse_args()
        self.args = args
        args.show_log = None
        args.det_model_dir = f"{ASK_DOC_OCR_INFERENCE_PATH}ch_PP-OCRv4_det_infer"
        args.rec_model_dir = f"{ASK_DOC_OCR_INFERENCE_PATH}ch_PP-OCRv4_rec_infer"
        args.table_model_dir = (
            f"{ASK_DOC_OCR_INFERENCE_PATH}ch_ppstructure_mobile_v2.0_SLANet_infer"
        )

        args.layout_model_dir = (
            f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/ppt_pdf_best.pt"
        )
        args.rec_char_dict_path = (
            f"{ASK_DOC_OCR_PATH}PaddleOCR/ppocr/utils/ppocr_keys_v1.txt"
        )
        args.table_char_dict_path = (
            f"{ASK_DOC_OCR_PATH}PaddleOCR/ppocr/utils/dict/table_structure_dict_ch.txt"
        )
        args.vis_font_path = f"{ASK_DOC_OCR_PATH}PaddleOCR/doc/fonts/simfang.ttf"

        self.language = language
        if self.language == "english":
            logger.info("use english model...")
            args.det_model_dir = f"{ASK_DOC_OCR_INFERENCE_PATH}en_PP-OCRv3_det_infer"
            args.rec_model_dir = f"{ASK_DOC_OCR_INFERENCE_PATH}en_PP-OCRv4_rec_infer"
            args.table_model_dir = (
                f"{ASK_DOC_OCR_INFERENCE_PATH}en_ppstructure_mobile_v2.0_SLANet_infer"
            )
            args.rec_char_dict_path = (
                f"{ASK_DOC_OCR_PATH}PaddleOCR/ppocr/utils/en_dict.txt"
            )
            args.table_char_dict_path = (
                f"{ASK_DOC_OCR_PATH}PaddleOCR/ppocr/utils/dict/table_structure_dict.txt"
            )
            args.layout_dict_path = f"{ASK_DOC_OCR_PATH}PaddleOCR/ppocr/utils/dict/layout_dict/layout_publaynet_dict.txt"

        self.bert_device = get_device(args.use_gpu_in_bert)
        self.yolo_device = get_device(args.use_gpu_in_yolo)
        self.paddle_device = args.use_gpu_in_paddle
        logger.info(
            f"yolo use: {self.yolo_device}; paddle use: {self.paddle_device}; bert use: {self.bert_device}"
        )

        # init model
        self._yolo_det_model = YOLO(args.layout_model_dir).to(self.yolo_device)
        # Doclayout模型
        self._doclayout_det_model = YOLOv10(
            f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/doclayout_yolo_docstructbench_imgsz1024.pt"
        ).to(self.yolo_device)

        # paddle表格检测模型
        self.table_det_model = create_pipeline(
            pipeline=f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/table_recognition_v2.yaml",
            device=args.use_gpu_in_paddle,
        )
        if args.use_gpu_in_paddle == "cpu":
            self._ocr_model = PaddleOCR(
                use_angle_cls=True, lang=self.language, use_gpu=False
            )
        elif args.use_gpu_in_paddle.startswith("gpu:"):
            self._ocr_model = PaddleOCR(
                use_angle_cls=True,
                lang=self.language,
                use_gpu=True,
                gpu_id=int(args.use_gpu_in_paddle.split(":")[-1]),
            )
        else:
            raise RuntimeError("GPU settings are incorrect")

        self.image_orientation_predictor = None
        if args.image_orientation:
            import paddleclas

            self.image_orientation_predictor = paddleclas.PaddleClas(
                model_name="text_image_orientation"
            )

        args.drop_score = 0
        self.label2id = {"A": 0, "B": 1, "0": "A", "1": "B"}
        self.tokenizer = BertTokenizer.from_pretrained(
            f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/bert-base-chinese"
        )
        self.myModel = MyBertModel(
            f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/bert-base-chinese",
            len(self.label2id) // 2,
            0.3,
        )
        self.myModel.load_state_dict(
            torch.load(f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/bert_best.pt")
        )
        self.myModel.to(self.bert_device)

    def convert_tensor(self, tokenizer: BertTokenizer, text: str, device):
        input_ids, attention_mask = [], []
        _input_ids, max_len = convert_to_id(tokenizer, text)
        for i_i in _input_ids:
            i_i_len = len(i_i)
            input_ids.append(i_i + [0 for _ in range(max_len - i_i_len)])
            attention_mask.append(
                [1 for _ in range(i_i_len)] + [0 for _ in range(max_len - i_i_len)]
            )

        return (
            torch.tensor(input_ids).to(device),
            torch.tensor(attention_mask).to(device),
        )

    def _bert_pred(self, text: str) -> str:
        input_tensor, attention_mask = self.convert_tensor(
            self.tokenizer, text, self.bert_device
        )
        logits = self.myModel(ids_inputs=input_tensor, attention_mask=attention_mask)
        probabilities = torch.nn.functional.softmax(logits, dim=-1)
        pred = [0 if p[0] >= 0.98 else 1 for p in probabilities.tolist()]  # 卡阈值
        pred_str = "".join([self.label2id[str(p)] for p in pred])

        # os.makedirs("/home/<USER>/Code/文档解析/results", exist_ok=True)
        # with open(
        #     f"/home/<USER>/Code/文档解析/results/{uuid.uuid1().hex}.json",
        #     "w",
        #     encoding="UTF-8",
        # ) as fw:
        #     json.dump(
        #         {"text": "\n".join(text.split("\n")), "label": pred_str},
        #         fw,
        #         ensure_ascii=False,
        #         indent=4,
        #     )

        return _format_text(text.split("\n"), pred_str)

    def _split_by_slice(self, texts: List[str]) -> str:
        if len(texts) < 2:
            return "".join(texts)

        pred_texts = [texts[0]]
        for text in texts[1:]:
            _text = pred_texts.pop()
            a, b = _text[:-50], _text[-50:]
            pred = self._bert_pred(f"{b}\n{text}")
            pred_texts.extend(f"{a}{pred}".split("\n"))

        return "\n".join(pred_texts)

    def _replace(self, text: str, olds: str) -> List[str]:
        for old in olds:
            text = text.replace(f"{old}\n", f"{old}\n\n\n\n\n")

        return text.split("\n\n\n\n\n")

    def bert_model_api(self, text: str, sign: str = "。；;") -> str:
        if text.count("\n") == 0:
            return text

        texts = []
        for t in self._replace(text, sign):
            if t.count("\n") == 0:
                texts.append(t)
            elif len(t) < 300:  # token数量小于300
                texts.append(self._bert_pred(t))
            else:
                texts.append(self._split_by_slice(t.split("\n")))

        return "\n".join(texts)

    def doclayout_rec(self, img_path: str) -> List[Dict]:
        yolo_reaults = self._doclayout_det_model(img_path, imgsz=1024, conf=0.5)[0]
        names = yolo_reaults.names
        bbox = yolo_reaults.boxes.xyxy.cpu().numpy().astype(int).tolist()
        labels = yolo_reaults.boxes.cls.cpu().numpy().astype(int).tolist()
        confs = yolo_reaults.boxes.conf.cpu().numpy().tolist()

        res = []
        for box, label_id, conf in zip(bbox, labels, confs):
            if cal_rect_area(box) <= 0 or names[label_id] in ("table",):
                continue

            label = (
                names[label_id] if names[label_id] in ("title", "figure") else "text"
            )
            res.append(
                {
                    "type": "doclayout",
                    "bbox": nums2str(box),
                    "label": label,
                    "conf": round(conf, 7),
                }
            )

        return res

    def ouryolo_rec(self, img_path: str) -> List[Dict]:
        yolo_reaults = self._yolo_det_model(img_path, conf=0.5)[0]
        names = yolo_reaults.names
        bbox = yolo_reaults.boxes.xyxy.cpu().numpy().astype(int).tolist()
        labels = yolo_reaults.boxes.cls.cpu().numpy().astype(int).tolist()
        confs = yolo_reaults.boxes.conf.cpu().numpy().tolist()
        res = []
        for box, label_id, conf in zip(bbox, labels, confs):
            if cal_rect_area(box) <= 0 or names[label_id] not in (
                "catalog",
                "header_title",
            ):
                continue

            res.append(
                {
                    "type": "ourlayout",
                    "bbox": nums2str(box),
                    "label": names[label_id],
                    "conf": round(conf, 7),
                }
            )

        return res

    def table_det(self, img_path: str) -> List[Dict]:
        res = []
        det_res = self.table_det_model.predict(
            input=img_path, use_doc_orientation_classify=False, use_doc_unwarping=False
        )
        for det in det_res:
            for dic in det["layout_det_res"]["boxes"]:
                if dic["label"] != "table":
                    continue

                res.append(
                    {
                        "type": "tablelayout",
                        "bbox": nums2str(map(int, dic["coordinate"])),
                        "label": dic["label"],
                        "conf": round(dic["score"], 7),
                    }
                )

        return res

    def yolo_rec(
        self, img_paths: Union[str, List[str]]
    ) -> Union[List[Layout], List[List[Layout]]]:
        res: List[List[Layout]] = []
        _img_paths = [img_paths] if isinstance(img_paths, str) else img_paths
        for img_path in _img_paths:
            doclayout = self.doclayout_rec(img_path)
            ourlayout = self.ouryolo_rec(img_path)
            table_det_res = self.table_det(img_path)
            res.append(doclayout + ourlayout + table_det_res)

        if isinstance(img_paths, str):
            return _remove_cover_label(res[0])

        return [_remove_cover_label(l_s) for l_s in res]

    def ocr_rec(
        self, img_paths: Union[str, List[str]]
    ) -> Union[List[OCR], List[List[OCR]]]:
        """
        Args:
            img_paths: 图片路径
        """
        _img_paths = [img_paths] if isinstance(img_paths, str) else img_paths

        res: List[List[OCR]] = []
        for img_path in _img_paths:
            try:
                ocr_results = self._ocr_model.ocr(img_path, cls=True)[0]
            except:
                logger.info(f"图片`{os.path.basename(img_path)}`已损坏")
                ocr_results = []

            res.append([])
            if not ocr_results:
                continue

            for ocr in ocr_results:
                # 文本坐标、文本、置信度
                pos, (text, confidence) = ocr
                # if '安全等' in text:
                #     print()
                simple_text = remove_punctuation(text, True).lower()
                if not simple_text or cal_rect_area(pos[0] + pos[2]) <= 0:
                    continue

                res[-1].append(
                    OCR(
                        bbox=nums2str(map(int, pos[0] + pos[2])),
                        origin_text=text,
                        text=simple_text,
                        font=int(abs(pos[2][1] - pos[0][1])),
                        confidence=confidence,
                    )
                )

        if isinstance(img_paths, str):
            return res[0]

        return res


# if __name__ == "__main__":
#     text = "44\n44121093\n44124911\n44125911\n44129920\n本国子目4412.1093、4412.4911、4412.5911、4412.9920所涉及的“热带木\n”，是指下列木材：大叶帽柱木、非洲桃花心木、西非红豆木、箭毒木、阿\n兰木、圭亚那苦油楝木、非洲甘比山榄木、杜楝木、非洲栎柞木、婆罗双木\n、美洲轻木、白驼峰楝木、黑驼峰楝木、卡蒂沃木、雪松木、西非褐红椴木\n、深红色红柳桉木，非洲核桃楝木、阿夫苏木、象牙海岸榄仁木、破布木、\n吉贝木、丝棉木、乔状黄牛木、安哥拉丛花木、巴西胡桃木、皮蚁木、伊罗\n科木、拟爱神木、夹竹桃木、巴西红木、绒根木、龙脑香木、开姆帕斯木、\n羯布罗香木、康多非洲楝木、象牙海岸褐红椴木、象牙海岸翼梧桐木、浅红\n色红柳桉木、非洲榄仁木、南美樟木、圭亚那铁线子木、西印度桃花心木、\n猴子果木、肖氏夸利亚木、曼孙梧桐木、马来蝴蝶木、巴栲红柳桉木、粗轴\n坡垒木、印茄木、斯温漆木、异翅香木、非洲梨木、非洲银叶木、胶木、非\n洲白梧桐木、加蓬榄木、蓖麻木、爱里古夷苏木、奥文科尔木、中非蜡烛木\n、紫檀木、人面子木、危地马拉黑黄檀木、印度黑黄檀木、巴西黑黄檀木、\n巴西柚、巴西花梨木、白坚木、鸡骨常山木、印马四出香木、大沃契希亚木\n、东西亚棱柱木、萨撇列木、萌生木棉木、苏帕楠木、西波木、苏古皮拉木\n、红椿木、圭亚那考拉玉蕊木、柚木、安哥拉香桃花心木、非洲阿勃木、南\n美肉豆蔻木、白柳桉木、白色红柳桉木、白色柳桉木、黄色红柳桉木。"
#     parseModel = ParseModel()
#     new_text = parseModel.bert_model_api(text)
#     print(new_text)
