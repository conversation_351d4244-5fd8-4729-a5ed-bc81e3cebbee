def calculate_Business_net_profit_rate(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储数据
    annual_data = {}

    # 添加风险阈值常量
    RISK_THRESHOLD = 0.05

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的营业净利率为{round(elastic, 2)}，低于5%，"
        risk_desc += (
            "营业净利率偏低或波动较大可能反映企业盈利能力不足，或存在收入确认不规范、成本费用异常波动等问题，需核查企业的收入真实性及成本费用管控情况。"
        )
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "net_profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取净利润
            if record["projectName"] == "四、净利润（净亏损以“-”号填列）":
                net_profit = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "net_profit": net_profit}
                else:
                    annual_data[year]["net_profit"] = net_profit

    # 计算营业净利率
    for year in annual_data:
        if annual_data[year]["revenue"] != 0:
            annual_data[year]["net_profit_rate"] = (
                annual_data[year]["net_profit"] / annual_data[year]["revenue"]
            )
        else:
            annual_data[year]["net_profit_rate"] = None

    result = []
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        net_profit = annual_data[year]["net_profit"]
        net_profit_rate = annual_data[year]["net_profit_rate"]

        if net_profit_rate is not None and net_profit_rate < RISK_THRESHOLD:
            risk_years[year] = net_profit_rate

        # 格式化
        net_profit_rate_formatted = (
            f"{round(net_profit_rate * 100, 2)}%"
            if net_profit_rate is not None
            else "--"
        )

        result.append(
            {
                "所属期": year,
                "净利润（元）": net_profit,
                "营业收入（元）": revenue,
                "营业净利率": net_profit_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_Total_assets_net_profit_rate(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 提取financeBalance数据
    balance_records = data["data"]["financeBalance"]

    # 初始化一个字典来存储数据
    annual_data = {}

    # 添加风险阈值常量
    RISK_THRESHOLD = 0.03

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}的总资产净利率为{round(elastic *100, 2)}%，低于3%，"
        risk_desc += "总资产净利率过低可能表明企业资产利用效率较差，或存在资产虚增、低效资产配置等问题，需关注企业资产管理及盈利能力的实际情况。"
        return risk_desc

    # 遍历financeBalance数据
    for record in balance_records:
        # 检查是否为年度财务报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]

            # 提取总资产
            if record["projectName"] == "资产总计":
                ending_balance = float(record["endingBalance"] or 0)
                if year not in annual_data:
                    annual_data[year] = {
                        "ending_balance": ending_balance,
                        "net_profit": 0,
                    }
                else:
                    annual_data[year]["ending_balance"] = ending_balance

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取净利润
            if record["projectName"] == "四、净利润（净亏损以“-”号填列）":
                net_profit = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {
                        "ending_balance": 0,
                        "net_profit": net_profit,
                    }
                else:
                    annual_data[year]["net_profit"] = net_profit

    # 计算平均总资产
    for year in annual_data:
        annual_data[year]["average_total_assets"] = annual_data[year]["ending_balance"]

    # 计算平均总资产净利率
    for year in annual_data:
        annual_data[year]["net_profit_rate"] = (
            annual_data[year]["net_profit"] / annual_data[year]["average_total_assets"]
            if annual_data[year]["average_total_assets"] != 0
            else None
        )

    # 格式化输出
    result = []
    for year in sorted(annual_data.keys()):
        net_profit = annual_data[year]["net_profit"]
        average_total_assets = annual_data[year]["average_total_assets"]
        net_profit_rate = annual_data[year]["net_profit_rate"]

        if net_profit_rate is not None and net_profit_rate < RISK_THRESHOLD:
            risk_years[year] = net_profit_rate

        net_profit_rate_formatted = (
            f"{round(net_profit_rate * 100, 2)}%"
            if net_profit_rate is not None
            else "--"
        )
        average_total_assets_formatted = (
            round(average_total_assets, 2) if average_total_assets is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "净利润（元）": net_profit,
                "平均总资产": average_total_assets_formatted,
                "总资产净利率": net_profit_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


def calculate_Equity_net_profit_rate(data):
    # 提取financeProfit数据
    profit_records = data["data"]["financeProfit"]

    # 提取financeBalance数据
    balance_records = data["data"]["financeBalance"]

    # 初始化一个字典来存储数据
    annual_data = {}

    # 添加风险阈值常量
    RISK_THRESHOLD = 0.08

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的权益净利率为{round(elastic * 100, 2)}%，低于8%，"
        risk_desc += (
            "权益净利率偏低或波动较大可能反映企业股东权益未能有效带来收益，或存在资本结构不合理、净利润异常波动等问题，需关注企业的盈利能力及资本管理效率。"
        )
        return risk_desc

    # 遍历financeBalance数据
    for record in balance_records:
        # 检查是否为年度财务报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]
            # 提取股东权益
            if record["projectName"] == "所有者权益（或股东权益）合计":
                ending_balance = float(record["endingBalance"] or 0)
                if year not in annual_data:
                    annual_data[year] = {
                        "ending_balance": ending_balance,
                        "profit": 0,
                    }
                else:
                    annual_data[year]["ending_balance"] = ending_balance

    # 遍历financeProfit数据
    for record in profit_records:
        # 检查是否为年度表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取利润总额
            if record["projectName"] == "四、净利润（净亏损以“-”号填列）":
                net_profit = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {
                        "ending_balance": 0,
                        "net_profit": net_profit,
                    }
                else:
                    annual_data[year]["net_profit"] = net_profit

    # 计算股东权益净利率
    for year in annual_data:
        annual_data[year]["net_profit_rate"] = (
            annual_data[year]["net_profit"] / annual_data[year]["ending_balance"]
        )

    # 格式化输出
    result = []
    for year in sorted(annual_data.keys()):
        net_profit = annual_data[year]["net_profit"]
        equity = annual_data[year]["ending_balance"]
        net_profit_rate = annual_data[year]["net_profit_rate"]

        if net_profit_rate is not None and net_profit_rate < RISK_THRESHOLD:
            risk_years[year] = net_profit_rate

        # 格式化
        net_profit_rate_formatted = (
            f"{round(net_profit_rate * 100, 2)}%"
            if net_profit_rate is not None
            else "--"
        )

        result.append(
            {
                "所属期": year,
                "净利润（元）": net_profit,
                "股东权益": equity,
                "权益净利率": net_profit_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description
