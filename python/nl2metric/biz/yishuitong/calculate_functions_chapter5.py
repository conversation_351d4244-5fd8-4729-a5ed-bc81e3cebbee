def calculate_sales_and_tax_elastic_modulus(data):
    """
    销售额变动率与增值税应纳税额变动率弹性系数检查
    参数:
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年销售额、应纳税额、销售额变动率、应纳税额变动率和弹性系数的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "销售额（元）": 2944.5,
                    "应纳税额（元）": 2944.5,
                    "销售额变动率": "32.1%",
                    "应纳税额变动率": "32.1%",
                    "弹性系数": 0.3
                },
                ...
            ]
        str: 风险描述
    """
    value_added = data["data"]["valueAdded"]
    annual_data = {}

    # 提取销售额和应纳税额
    for value in value_added:
        year = value["endDate"][:4]
        # 销售额
        if value["projectName"] == "（一）应征增值税不含税销售额（3%征收率）":
            sales_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"sales_amt": sales_amt, "tax_amt": 0}
            else:
                annual_data[year]["sales_amt"] += sales_amt

        # 应纳税额
        if value["projectName"] == "本期应纳税额":
            tax_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"sales_amt": 0, "tax_amt": tax_amt}
            else:
                annual_data[year]["tax_amt"] += tax_amt

    # 按年份排序
    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率和弹性系数
    result = []
    risk = []
    risk_desc = ""
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 销售额变动率
            if annual_data[prev_year]["sales_amt"] != 0:
                sales_growth_rate = (
                    annual_data[year]["sales_amt"] - annual_data[prev_year]["sales_amt"]
                ) / annual_data[prev_year]["sales_amt"]
            else:
                sales_growth_rate = None  # 上一年销售额为 0，无法计算变动率

            # 应纳税额变动率
            if annual_data[prev_year]["tax_amt"] != 0:
                tax_growth_rate = (
                    annual_data[year]["tax_amt"] - annual_data[prev_year]["tax_amt"]
                ) / annual_data[prev_year]["tax_amt"]
            else:
                tax_growth_rate = None  # 上一年应纳税额为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            sales_growth_rate = None
            tax_growth_rate = None

        # 计算弹性系数
        if (
            sales_growth_rate is not None
            and tax_growth_rate is not None
            and tax_growth_rate != 0
        ):
            elastic_modulus = (sales_growth_rate / tax_growth_rate) * 100
        else:
            elastic_modulus = None  # 无法计算弹性系数

        # 格式化输出
        formatted_sales_growth_rate = (
            f"{round(sales_growth_rate * 100, 2)}%"
            if sales_growth_rate is not None
            else "--"
        )
        formatted_tax_growth_rate = (
            f"{round(tax_growth_rate * 100, 2)}%"
            if tax_growth_rate is not None
            else "--"
        )
        formatted_elastic_modulus = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "销售额（元）": round(annual_data[year]["sales_amt"], 2)
                if annual_data[year]["sales_amt"] is not None
                else "--",
                "应纳税额（元）": round(annual_data[year]["tax_amt"], 2)
                if annual_data[year]["tax_amt"] is not None
                else "--",
                "销售额变动率": formatted_sales_growth_rate,
                "应纳税额变动率": formatted_tax_growth_rate,
                "弹性系数": formatted_elastic_modulus,
            }
        )

        # 风险识别：检查弹性系数是否在合理范围（0.8 - 1.2）
        if elastic_modulus is not None and (
            elastic_modulus < 0.8 or elastic_modulus > 1.2
        ):
            risk.append({"年份": year, "弹性系数": formatted_elastic_modulus})
    risk_desc = f"风险描述: "
    if risk:
        for i in risk:
            risk_desc += f"{i['年份']}年度，弹性系数为{i['弹性系数']}，"
        risk_desc += f"弹性系数异常可能指示存在销售额或应纳税额的异常波动，需要进一步核查。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc


def calculate_tax_growth_rate(current_date, data):
    """
    增值税应纳税额同比变动率检查
    参数:
        current_date
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年应纳税额和应纳税额变动率的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "应纳税额（元）": 2944.5,
                    "应纳税额变动率": "32.1%"
                },
                ...
            ]
        str: 风险描述
    """
    value_added = data["data"]["valueAdded"]
    annual_data = {}
    # 获取当前年份
    current_year = int(current_date[:4])
    start_year = current_year - 3

    # 提取应纳税额
    for value in value_added:
        year = value["endDate"][:4]
        # 仅处理过去3年内的数据
        if int(year) < start_year:
            continue
        # 应纳税额
        if value["projectName"] == "本期应纳税额":
            tax_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"tax_amt": tax_amt}
            else:
                annual_data[year]["tax_amt"] += tax_amt

    # 按年份排序
    sorted_years = sorted(annual_data.keys(), reverse=False)
    # 计算变动率
    result = []
    risks = []
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 应纳税额变动率
            if annual_data[prev_year]["tax_amt"] != 0:
                tax_growth_rate = (
                    annual_data[year]["tax_amt"] - annual_data[prev_year]["tax_amt"]
                ) / annual_data[prev_year]["tax_amt"]
            else:
                tax_growth_rate = None  # 上一年应纳税额为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            tax_growth_rate = None

        if tax_growth_rate is not None and abs(tax_growth_rate) > 0.5:
            growth_or_decline = "增长" if tax_growth_rate > 0 else "下降"
            risks.append(
                {
                    "year": year,
                    "growth_or_decline": growth_or_decline,
                    "tax_growth_rate": round(abs(tax_growth_rate * 100), 2),
                }
            )

        # 格式化输出
        result.append(
            {
                "所属期": year,
                "应纳税额（元）": round(annual_data[year]["tax_amt"], 2),
                "应纳税额变动率": f"{round(tax_growth_rate * 100, 2)}%"
                if tax_growth_rate is not None
                else "--",
            }
        )

    risk_desc = "风险描述：近三年内，"
    if risks:
        descriptions = [
            f"{risk['year']}年{risk['growth_or_decline']}{risk['tax_growth_rate']}%"
            for risk in risks
        ]
        risk_desc += "、".join(descriptions)
        risk_desc += "。大幅波动可能指示企业经营状况或税务申报存在异常。\n"
    else:
        risk_desc += "该指标项未检测到风险"

    return result, risk_desc


def calculate_revenue_sales_diff(current_date, data):
    """
    营业收入与销售额比对
    参数:
        current_date
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年营业收入、增值税销售额和差额的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "营业收入": 2944.5,
                    "增值税销售额（元）": 2944.5,
                    "差额（元）": 0
                },
                ...
            ]
    """
    value_added = data["data"]["valueAdded"]
    income_records = data["data"]["corporateIncome"]
    annual_data = {}
    # 获取当前年份
    current_year = int(current_date[:4])
    start_year = current_year - 3

    # 提取销售额
    for value in value_added:
        year = value["endDate"][:4]
        # 仅处理过去3年内的数据
        if int(year) < start_year:
            continue
        # 销售额
        if value["projectName"] == "（一）应征增值税不含税销售额（3%征收率）":
            sales_amt = float(value["shouldPayAmt"] or 0)
            if year not in annual_data:
                annual_data[year] = {"revenue": 0, "sales_amt": sales_amt}
            else:
                annual_data[year]["sales_amt"] += sales_amt

    # 提取营业收入
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            year = record["endDate"][:4]  # 从endDate中提取年份
            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "sales_amt": 0}
                else:
                    annual_data[year]["revenue"] = revenue

    # 计算差额并格式化输出
    result = []
    risks = []
    for year in sorted(annual_data.keys()):
        revenue = annual_data[year]["revenue"]
        sales_amt = annual_data[year]["sales_amt"]
        diff = revenue - sales_amt

        if diff is not None and revenue != 0 and abs(diff / revenue) > 0.1:
            risks.append(
                {
                    "year": year,
                    "revenue": revenue,
                    "sales_amt": sales_amt,
                    "diff": round(diff, 2),
                }
            )

        result.append(
            {
                "所属期": year,
                "营业收入": round(revenue, 2) if revenue is not None else "--",
                "增值税销售额（元）": round(sales_amt, 2) if sales_amt is not None else "--",
                "差额（元）": round(diff, 2) if diff is not None else "--",
            }
        )

    risk_desc = f"风险描述: "
    if risks:
        risk_desc += f"近三年内，"
        for i in risks:
            risk_desc += f"{i['year']}年所得税申报的营业收入为{round(i['revenue'], 2)}元，增值税申报的销售额为{round(i['sales_amt'], 2)}元，差额为{round(i['diff'], 2)}元，"
        risk_desc += f"较大差异可能指示存在收入不一致的风险，需要核实两者的差异原因。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc


def calculate_revenue_diff(current_date, data):
    """
    企业所得税营业收入与利润表的营业收入比对
    参数:
        current_date
        data (dict): 包含企业财税数据的字典
    返回:
        list: 包含每年企业所得税营业收入、利润表的营业收入和差额的列表，格式如下：
            [
                {
                    "所属期": "2021",
                    "年报营业收入": 2944.5,
                    "财报营业收入": 2944.5,
                    "差额（元）": 0
                },
                ...
            ]
    """
    annual_data = {}
    # 获取当前年份
    current_year = int(current_date[:4])
    start_year = current_year - 3

    # 提取利润表的营业收入
    financeProfit = data["data"]["financeProfit"]
    for profit in financeProfit:
        if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
            year = profit["endDate"][:4]
            if int(year) < start_year:
                continue
            profit_revenue = float(profit["currentYearAccumulativeAmount"] or 0)
            if year not in annual_data:
                annual_data[year] = {"tax_revenue": 0, "profit_revenue": profit_revenue}
            else:
                annual_data[year]["profit_revenue"] = profit_revenue

    # 提取企业所得税营业收入
    income_records = data["data"]["corporateIncome"]
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
            year = record["endDate"][:4]  # 从endDate中提取年份
            if int(year) < start_year:
                continue
            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                tax_revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {
                        "tax_revenue": tax_revenue,
                        "profit_revenue": 0,
                    }
                else:
                    annual_data[year]["tax_revenue"] = tax_revenue

    # 计算差额并格式化输出
    result = []
    risks = []
    for year in sorted(annual_data.keys()):
        tax_revenue = annual_data[year]["tax_revenue"]
        profit_revenue = annual_data[year]["profit_revenue"]
        diff = tax_revenue - profit_revenue

        if diff is not None and tax_revenue != 0 and abs(diff / tax_revenue) > 0.1:
            risks.append(
                {
                    "year": year,
                    "tax_revenue": tax_revenue,
                    "profit_revenue": profit_revenue,
                    "diff": round(diff, 2),
                }
            )

        result.append(
            {
                "所属期": year,
                "年报营业收入": tax_revenue,
                "财报营业收入": profit_revenue,
                "差额（元）": diff,
            }
        )

    risk_desc = f"风险描述: "
    if risks:
        risk_desc += f"近三年内，"
        for i in risks:
            risk_desc += f"{i['year']}年所得税申报的营业收入为{i['tax_revenue']}元，利润表中的营业收入为{i['profit_revenue']}元，差额为{i['diff']}元，"
        risk_desc += f"较大差异可能指示存在收入不一致的风险，需要核实两者的差异原因。\n"
    else:
        risk_desc += f"该指标项未检测到风险"

    return result, risk_desc
