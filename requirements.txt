absl-py==2.2.2
accelerate==0.28.0
aioboto3==14.3.0
aiobotocore==2.22.0
aiofiles==24.1.0
aiohttp==3.9.3
aioitertools==0.12.0
aiolimiter==1.1.0
aiomysql==0.2.0
aiosignal==1.3.1
aiosmtplib==4.0.1
aiostream==0.5.2
alibabacloud-dysmsapi20170525==3.1.1
alibabacloud-tea==0.4.2
alibabacloud_credentials==0.3.6
alibabacloud_endpoint_util==0.0.3
alibabacloud_gateway_spi==0.0.3
alibabacloud_openapi_util==0.2.2
alibabacloud_tea_openapi==0.3.13
alibabacloud_tea_util==0.3.13
alibabacloud_tea_xml==0.0.2
annotated-types==0.6.0
anyio==4.4.0
appnope==0.1.4
astor==0.8.1
astroid==2.15.8
asttokens==2.4.1
attrdict==2.0.1
attrs==23.2.0
babel==2.16.0
backoff==2.2.1
bce-python-sdk==0.9.23
beautifulsoup4==4.12.3
black==23.3.0
blinker==1.7.0
bm25s==0.1.10
boto3==1.37.3
botocore==1.37.3
cachetools==5.5.0
certifi==2024.2.2
cffi==1.17.1
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
comm==0.2.2
contourpy==1.3.1
cryptography==43.0.3
cssselect==1.2.0
cssutils==2.11.1
cycler==0.12.1
Cython==3.0.11
dataclasses-json==0.5.14
datasets==2.18.0
debugpy==1.8.7
decorator==5.1.1
deepdiff==8.0.1
Deprecated==1.2.14
dill==0.3.8
dirtyjson==1.0.8
distro==1.9.0
ecloudsdkcore==1.0.5
ecloudsdkvpc==1.0.21
environs==9.5.0
et_xmlfile==2.0.0
executing==2.1.0
faiss-cpu==1.7.4
fastapi==0.112.2
filelock==3.13.1
filetype==1.2.0
fire==0.7.0
fireworks-ai==0.12.1
FlagEmbedding==1.2.5
flake8==6.1.0
Flask==2.3.3
flask-babel==4.0.0
fonttools==4.55.0
frozenlist==1.4.1
fsspec==2023.12.2
future==1.0.0
gitdb==4.0.12
GitPython==3.1.44
googleapis-common-protos==1.59.1
greenlet==3.2.2
grpcio==1.60.0
h11==0.14.0
hanlp==2.1.0b56
hanlp-common==0.0.19
hanlp-downloader==0.0.25
hanlp-trie==0.0.5
html2text==2024.2.26
httpcore==1.0.4
httptools==0.6.1
httpx==0.27.0
httpx-sse==0.4.0
huggingface-hub==0.21.4
idna==3.7
imageio==2.36.0
imgaug==0.4.0
importlib-metadata==6.11.0
iniconfig==2.0.0
ipykernel==6.29.5
ipython==8.28.0
isort==5.13.2
itsdangerous==2.1.2
jedi==0.19.1
jieba==0.42.1
Jinja2==3.1.3
jmespath==1.0.1
joblib==1.3.2
jsonpatch==1.33
jsonpointer==2.4
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.7
langchain==0.2.11
langchain-community==0.2.10
langchain-core==0.2.43
langchain-openai==0.1.17
langchain-text-splitters==0.2.4
langfuse==2.46.0
langsmith==0.1.144
lazy-object-proxy==1.10.0
lazy_loader==0.4
llama-cloud==0.0.6
llama-index==0.10.58
llama-index-agent-openai==0.2.9
llama-index-cli==0.1.13
llama-index-core==0.10.58
llama-index-embeddings-langchain==0.1.2
llama-index-embeddings-openai==0.1.11
llama-index-indices-managed-llama-cloud==0.2.4
llama-index-legacy==0.9.48
llama-index-llms-openai==0.1.27
llama-index-multi-modal-llms-openai==0.1.8
llama-index-program-openai==0.1.6
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.30
llama-index-readers-llama-parse==0.1.6
llama-index-retrievers-bm25==0.2.2
llama-index-vector-stores-faiss==0.1.2
llama-index-vector-stores-milvus==0.1.20
llama-parse==0.4.9
llamaindex-py-client==0.1.19
lmdb==1.5.1
loguru==0.7.2
lxml==5.3.0
Markdown==3.5.2
markdown-it-py==2.2.0
MarkupSafe==2.1.5
marshmallow==3.21.1
matplotlib==3.9.2
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
milvus-lite==2.4.8
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
multiprocess==0.70.16
mypy==1.8.0
mypy-extensions==1.0.0
mysql-connector==2.2.9
mysql-connector-python==8.4.0
mysql-connector-python-rf==2.2.2
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.8.1
numpy==1.24.4
openai==1.39.0
opencv-contrib-python==********
opencv-python==********
opencv-python-headless==*********
openpyxl==3.1.2
opentelemetry-api==1.21.0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-jaeger-proto-grpc==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0
opentelemetry-instrumentation==0.42b0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-grpc==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-wsgi==0.42b0
opentelemetry-sdk==1.21.0
opentelemetry-semantic-conventions==0.42b0
opentelemetry-util-http==0.42b0
opt-einsum==3.3.0
orderly-set==5.2.2
orjson==3.10.0
packaging==24.1
paddleocr==2.7.3
paddlepaddle==2.6.1
pandas==2.0.2
parso==0.8.4
pathspec==0.12.1
pdf2docx==0.5.8
pdf2image==1.17.0
pdfkit==1.0.0
pdfminer.six==20221105
pdfplumber==0.10.3
pexpect==4.9.0
phrasetree==0.0.8
pillow==10.2.0
pillow_heif==0.22.0
platformdirs==4.2.0
playwright==1.52.0
pluggy==1.5.0
poe-api-wrapper==1.3.7
premailer==3.10.0
prompt_toolkit==3.0.48
protobuf==4.25.3
psutil==5.9.8
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarmor==9.1.2
pyarmor.cli.core==7.6.5
pyarrow==15.0.2
pyarrow-hotfix==0.6
pybboxes==0.1.6
pyclipper==1.3.0.post6
pycodestyle==2.11.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.6.4
pydantic_core==2.16.3
pyee==13.0.0
pyflakes==3.1.0
Pygments==2.17.2
pyinstrument==4.5.1
PyJWT==2.8.0
pylint==2.17.5
pymilvus==2.5.5
PyMuPDF==1.24.14
PyMySQL==1.1.0
pynvml==11.5.0
pyparsing==3.2.0
pypdf==4.3.1
PyPDF2==3.0.1
pypdfium2==4.30.0
PyStemmer==*******
pytesseract==0.3.10
pytest==8.0.1
pytest-asyncio==0.26.0
python-dateutil==2.9.0.post0
python-docx==0.8.11
python-dotenv==1.0.1
python-json-logger==2.0.7
python-markdown-math==0.8
python-multipart==0.0.20
python-pptx==0.6.23
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.2.0
qianfan==0.3.1
RapidFuzz==3.10.1
rarfile==4.2
ray==2.34.0
readerwriterlock==1.0.9
redis==5.0.1
referencing==0.35.1
regex==2023.12.25
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.3.4
roboflow==1.1.64
rpds-py==0.20.0
s3fs==2023.12.2
s3transfer==0.11.3
safetensors==0.4.2
sahi==0.11.23
scikit-image==0.24.0
scikit-learn==1.4.1.post1
scipy==1.12.0
seaborn==0.13.2
sentence-transformers==2.2.2
sentencepiece==0.2.0
shapely==2.0.6
shellingham==1.5.4
simplejson==3.20.1
six==1.16.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.5
sql_metadata==2.10.0
SQLAlchemy==2.0.25
sqlparse==0.4.4
stack-data==0.6.3
starlette==0.38.6
striprtf==0.0.26
sympy==1.12
tenacity==8.2.3
tensorboard==2.19.0
tensorboard-data-server==0.7.2
termcolor==2.4.0
terminaltables==3.1.10
thop==0.1.1.post2209072238
threadpoolctl==3.4.0
thrift==0.16.0
tifffile==2024.9.20
tiktoken==0.8.0
tokenizers==0.15.2
tomli==2.0.1
tomlkit==0.12.4
toposort==1.5
torch==2.2.1
torchvision==0.17.1
tornado==6.4.1
tqdm==4.66.1
traitlets==5.14.3
transformers==4.39.0
typer==0.13.1
types-decorator==5.1.8.20240106
types-PyMySQL==*******
types-PyYAML==*********
types-requests==2.25.0
types-setuptools==69.0.0.20240125
types-waitress==2.1.4.20240106
typing-inspect==0.9.0
typing_extensions==4.9.0
tzdata==2024.1
ujson==5.10.0
ultralytics==8.2.51
ultralytics-thop==2.0.12
urllib3==1.26.18
uvicorn==0.30.6
visualdl==2.5.3
waitress==2.1.2
wcwidth==0.2.13
websocket-client==1.7.0
Werkzeug==3.0.1
wrapt==1.16.0
xinference-client==0.14.1
XlsxWriter==3.2.0
xxhash==3.4.1
yarl==1.9.4
yolov10==0.0.1
yolov5==7.0.14
zhipuai==2.1.4
zhon==2.0.2
zipp==3.18.1
