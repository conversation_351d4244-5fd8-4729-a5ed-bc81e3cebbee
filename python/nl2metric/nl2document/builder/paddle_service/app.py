import logging
import sys
from typing import Dict, List
from fastapi import FastAPI, HTTPException
import uvicorn
from paddleocr import PaddleOCR
from paddlex import create_pipeline, create_model
from paddlex.model import _ModelBasedInference
from models import ImageRequest, ImageResponse, OCRResult
from utils import (
    base64_to_numpy,
    get_ocrs, 
    remove_punctuation, 
    cal_rect_area, 
    nums2str
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 获取设备
def get_device(device: str = None):
    return "cpu"


app = FastAPI(title="PaddleOCR图像检测服务")

ASK_DOC_OCR_PATH = ""

# 全局变量存储模型
paddle_model = None
table_det_model = None
table_cell_det_model = None

@app.on_event("startup")
async def startup_event():
    """服务启动时加载模型"""
    global paddle_model, table_det_model, table_cell_det_model
    try:
        """
        /root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer/
        /root/.paddleocr/whl/det/ch/ch_PP-OCRv4_det_infer/
        /root/.paddleocr/whl/rec/ch/ch_PP-OCRv4_rec_infer/
        """
        device = get_device()
        if device == "cpu":
            paddle_model = PaddleOCR(
                use_angle_cls=True, lang="ch", use_gpu=False
            )
        else:
            paddle_model = PaddleOCR(
                use_angle_cls=True, lang="ch", use_gpu=True, gpu_id=0
            )
        table_det_model = create_pipeline(
            pipeline=f"{ASK_DOC_OCR_PATH}runs/detect/train39/weights/table_recognition_v2.yaml",
            device=get_device(),
        )
        model_dir = ""
        model_name = "RT-DETR-L_wired_table_cell_det"
        table_cell_det_model = create_model(model_name=model_name, model_dir=model_dir, device=get_device())
        logger.info("Model loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        raise RuntimeError("Model initialization failed")

@app.post("/paddle-ocr/inference")
async def paddle_ocr_inference(request: ImageRequest):
    """
    paddle OCR推理
    """
    base64_image = request.image
    img_array = base64_to_numpy(base64_image)
    try:
        ocr_results = paddle_model.ocr(img_array, cls=True)[0]
        res = []
        if not ocr_results:
            return ImageResponse(
                code=200,
                message="success",
                data={
                    "ocr_results": []
                }
            )
        for ocr in ocr_results:
            # 文本坐标、文本、置信度
            pos, (text, confidence) = ocr
            simple_text = remove_punctuation(text, True).lower()
            if not simple_text or cal_rect_area(pos[0] + pos[2]) <= 0:
                continue
            res.append(OCRResult(bbox=nums2str(map(int, pos[0] + pos[2])), 
                                 text=simple_text, 
                                 confidence=confidence, origin_text=text, 
                                 font=int(abs(pos[2][1] - pos[0][1]))))
    except Exception as e:
        logger.error(f"Failed to ocr: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return ImageResponse(
        code=200,
        message="success",
        data={
            "ocr_results": res
        }
    )

@app.post("/table-det/inference")
async def table_det_inference(request: ImageRequest):
    base64_image = request.image
    try:
        img_array = base64_to_numpy(base64_image=base64_image)
        res = []
        det_res = table_det_model.predict(
                input=img_array, use_doc_orientation_classify=False, use_doc_unwarping=False
        )
        for table in det_res:
            for dic in table["table_res_list"]:
                ocrs: List[Dict] = get_ocrs(
                    texts=dic["table_ocr_pred"]["rec_texts"],
                    bboxs=dic["table_ocr_pred"]["rec_boxes"].tolist(),
                    confs=dic["table_ocr_pred"]["rec_scores"].tolist(),
                )
                res.extend(ocrs)
        return ImageResponse(
            code=200,
            message="success",
            data={
                "ocr_results": res
            }
        )
    except Exception as e:
        logger.error(f"Failed to convert base64 image to numpy array: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/table-cell-det/inference")
async def table_cell_det_inference(request: ImageRequest):
    base64_image = request.image
    try:
        img_array = base64_to_numpy(base64_image=base64_image)
        res = []
        det_res = table_cell_det_model.predict(
                input=img_array, threshold=request.threshold, batch_size=request.batch_size
        )
        for out in det_res:
            for dic in out["boxes"]:
                x1, y1, x2, y2 = dic["coordinate"]
                res.extend(OCRResult(
                    bbox=nums2str(map(int, [x1, y1, x2, y2])), 
                    label="cell", 
                    confidence=round(dic["score"], 3)
                ))
        return ImageResponse(
            code=200,
            message="success",
            data={
                "ocr_results": res
            }
        )
    except Exception as e:
        logger.error(f"failed to execute table cell det: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)