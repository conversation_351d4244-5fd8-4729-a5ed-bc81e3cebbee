from pydantic import BaseModel, Field
from typing import Dict, Any

class ImageRequest(BaseModel):
    image: str = Field(..., description="Base64编码的图片")
    threshold: float = Field(default=0.5, description="置信度阈值")
    batch_size: int = Field(default=1, description="批处理大小")

class OCRResult(BaseModel):
    bbox: str = Field(..., description="边界框坐标")
    text: str = Field(default="", description="识别的文本")
    font: int = Field(default=0, description="字体大小")
    origin_text: str = Field(default="", description="原始文本")
    confidence: float = Field(..., description="置信度")
    label: str = Field(default="text", description="标签类型")

class ImageResponse(BaseModel):
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")