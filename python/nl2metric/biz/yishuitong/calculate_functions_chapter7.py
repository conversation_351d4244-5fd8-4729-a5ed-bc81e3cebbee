import json


# 7.1
def calculate_period_expense_tax_elastic_modulus(data):
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 添加风险年份记录
    risk_years = {}

    # 添加风险阈值常量
    RISK_THRESHOLD = 0.3

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的期间费用变动率与所得税贡献变动率为{round(elastic, 2)}，超过±30%，"
        risk_desc += "期间费用与所得税贡献变动不匹配可能表明费用核算不合理、税务筹划不当或利润操纵行为，需核实期间费用的真实性及税务申报的合规性。"
        return risk_desc

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "tax": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取应纳所得税额
            if record["projectName"] == "八、实际应纳所得税额（28+29-30）":
                tax = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "tax": tax}
                else:
                    annual_data[year]["tax"] = tax

            # 提取管理费用
            if record["projectName"] == "减：管理费用（填写A104000）":
                expense_admin = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_admin"] = expense_admin
            # 提取销售费用
            if record["projectName"] == "减：销售费用（填写A104000）":
                expense_sales = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_sales"] = expense_sales
            # 提取财务费用
            if record["projectName"] == "减：财务费用（填写A104000）":
                expense_finance = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_finance"] = expense_finance

    # 计算期间费用（管理费用、销售费用、财务费用）
    for year in annual_data:
        annual_data[year]["period_expense"] = (
            annual_data[year].get("expense_admin", 0)
            + annual_data[year].get("expense_sales", 0)
            + annual_data[year].get("expense_finance", 0)
        )

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 期间费用变动率
            if annual_data[prev_year]["period_expense"] != 0:
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["period_expense"]
                    - annual_data[prev_year]["period_expense"]
                ) / annual_data[prev_year]["period_expense"]
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 应纳税额变动率
            if annual_data[prev_year]["tax"] != 0:
                annual_data[year]["tax_growth_rate"] = (
                    annual_data[year]["tax"] - annual_data[prev_year]["tax"]
                ) / annual_data[prev_year]["tax"]
            else:
                annual_data[year]["tax_growth_rate"] = None  # 上一年应纳税额为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None
            annual_data[year]["tax_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["period_expense_rate"]
        tax_growth_rate = annual_data[year]["tax_growth_rate"]
        if (
            sales_growth_rate is not None
            and tax_growth_rate is not None
            and tax_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / tax_growth_rate
            ) * 100
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    for year in sorted_years:
        period_expense = annual_data[year]["period_expense"]
        revenue = annual_data[year]["revenue"]
        tax = annual_data[year]["tax"]
        revenue_growth_rate = annual_data[year]["period_expense_rate"]
        tax_growth_rate = annual_data[year]["tax_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        # 检查是否存在风险
        if elastic_modulus is not None and abs(elastic_modulus) > (RISK_THRESHOLD):
            risk_years[year] = elastic_modulus

        # 格式化变动率和弹性系数
        period_expense_formatted = (
            round(period_expense, 2) if period_expense is not None else "--"
        )
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        tax_growth_rate_formatted = (
            f"{round(tax_growth_rate * 100, 2)}%"
            if tax_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "期间费用（元）": period_expense_formatted,
                "营业收入（元）": revenue,
                "实际应纳所得税额（元）": tax,
                "期间费用变动率": revenue_growth_rate_formatted,
                "所得税贡献变动率": tax_growth_rate_formatted,
                "期间费用与企业所得税贡献变动系数": elastic_modulus_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.2
def expense_admin_analyzer(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 添加风险年份记录
    risk_years = {}

    # 设置风险阈值
    RISK_THRESHOLD = 0.15

    # 嵌套函数：生成风险描述
    def generate_risk_description(risk_years):
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, rate in risk_years.items():
            risk_desc += f"{year}年的管理费用与营业收入比值变动值为{round(rate, 2)}，超过±15%，"
        risk_desc += "管理费用变动过大或占比异常可能反映费用分摊不合理、企业管理效率下降或费用虚增，需进一步核实管理费用的真实性和必要性。"
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取管理费用
            if record["projectName"] == "管理费用":
                expense_admin = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算管理费用与营业收入比值变动值
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 管理费用与营业收入比值变动值
            if (
                annual_data[prev_year]["expense_admin"] != 0
                and annual_data[year]["revenue"] != 0
                and annual_data[prev_year]["revenue"] != 0
            ):
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["expense_admin"] / annual_data[year]["revenue"]
                    - annual_data[prev_year]["expense_admin"]
                    / annual_data[prev_year]["revenue"]
                )
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None

    # 格式化输出
    result = []
    for year in sorted_years:
        period_expense = annual_data[year]["expense_admin"]
        revenue = annual_data[year]["revenue"]
        period_expense_rate = annual_data[year]["period_expense_rate"]

        if period_expense_rate is not None and abs(period_expense_rate) > (
            RISK_THRESHOLD * 100
        ):
            risk_years[year] = period_expense_rate

        # 格式化
        period_expense_rate_formatted = (
            round(period_expense_rate, 2) if period_expense_rate is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "管理费用（元）": round(expense_admin, 2),
                "营业收入（元）": round(revenue, 2),
                "管理费用与营业收入比值变动值": period_expense_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.3
def expense_sell_analyzer(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 设置风险阈值
    RISK_THRESHOLD = 0.2

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, rate in risk_years.items():
            risk_desc += f"{year}年的销售费用与营业收入比值变动值为{round(rate, 2)}，超过±20%，"
        risk_desc += "销售费用与营业收入变动不匹配可能表明费用虚增或销售策略调整无效，需关注费用的合理性及其对收入增长的作用。"
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度财报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取销售费用
            if record["projectName"] == "销售费用":
                expense_admin = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算销售费用与营业收入比值变动值
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 销售费用与营业收入比值变动值
            if (
                annual_data[prev_year]["expense_admin"] != 0
                and annual_data[year]["revenue"] != 0
                and annual_data[prev_year]["revenue"] != 0
            ):
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["expense_admin"] / annual_data[year]["revenue"]
                    - annual_data[prev_year]["expense_admin"]
                    / annual_data[prev_year]["revenue"]
                )
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None

    # 格式化输出
    result = []
    for year in sorted_years:
        period_expense = annual_data[year]["expense_admin"]
        revenue = annual_data[year]["revenue"]
        period_expense_rate = annual_data[year]["period_expense_rate"]

        if period_expense_rate is not None and abs(period_expense_rate) > (
            RISK_THRESHOLD * 100
        ):
            risk_years[year] = period_expense_rate

        # 格式化
        period_expense_rate_formatted = (
            round(period_expense_rate, 2) if period_expense_rate is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "销售费用（元）": round(period_expense, 2),
                "营业收入（元）": round(revenue, 2),
                "销售费用与营业收入比值变动值": period_expense_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.4
def expense_fin_analyzer(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 设置风险阈值
    RISK_THRESHOLD = 0.25

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, rate in risk_years.items():
            risk_desc += f"{year}年的财务费用与营业收入比值变动值为{round(rate, 2)}，超过±25%，"
        risk_desc += "财务费用变动过大可能反映企业融资成本异常、借贷规模不合理或财务费用核算不准确，需重点关注企业的资金使用及财务管理状况。"
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度财报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取财务费用
            if record["projectName"] == "财务费用":
                expense_admin = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算财务费用与营业收入比值变动值
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 财务费用与营业收入比值变动值
            if (
                annual_data[prev_year]["expense_admin"] != 0
                and annual_data[year]["revenue"] != 0
                and annual_data[prev_year]["revenue"] != 0
            ):
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["expense_admin"] / annual_data[year]["revenue"]
                    - annual_data[prev_year]["expense_admin"]
                    / annual_data[prev_year]["revenue"]
                )
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None
            annual_data[year]["tax_growth_rate"] = None

    # 格式化输出
    result = []
    for year in sorted_years:
        period_expense = annual_data[year]["expense_admin"]
        revenue = annual_data[year]["revenue"]
        period_expense_rate = annual_data[year]["period_expense_rate"]

        if period_expense_rate is not None and abs(period_expense_rate) > (
            RISK_THRESHOLD
        ):
            risk_years[year] = period_expense_rate

        # 格式化
        period_expense_rate_formatted = (
            round(period_expense_rate, 2) if period_expense_rate is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "财务费用（元）": round(expense_admin, 2),
                "营业收入（元）": round(revenue, 2),
                "财务费用与营业收入比值变动值": period_expense_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.5
def union_fund_analyzer(data):
    # 提取financeProfit数据
    profits = data["data"]["financeProfit"]
    income_records = data["data"]["financeBalance"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        desc = "风险描述："
        for year, ratio in risk_years.items():
            desc += f"{year}年的工会经费支出占工资薪金支出的{ratio}%，超过2%，"
        desc += "工会经费超支可能导致无法足额税前扣除，增加企业税负，也可能反映工会经费管理不规范，需关注支出是否合规。"
        return desc

    for profit in profits:
        if profit["period"] == "Year":
            year = profit["endDate"][:4]

            # 提取财务费用
            if profit["projectName"] == "管理费用":
                expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度财报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "应付职工薪酬":
                revenue = float(record["endingBalance"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 计算超支比例
            salary = annual_data[year]["expense_admin"]
            if salary > 0:
                ratio = (annual_data[year]["revenue"] / salary) * 100
                if ratio > 2:
                    risk_years[year] = round(ratio, 2)

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 格式化输出
    result = []
    for year in sorted_years:
        result.append(
            {
                "所属期": year,
                "工会经费支出（元）": round(annual_data[year]["expense_admin"], 2),
                "工资薪金支出税收金额（元）": round(annual_data[year]["revenue"], 2),
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.6
def employee_welfare_fund_analyzer(data):
    # 提取financeProfit数据
    profits = data["data"]["financeProfit"]
    income_records = data["data"]["financeBalance"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        desc = "风险描述："
        for year, ratio in risk_years.items():
            desc += f"{year}年的职工福利费支出占工资薪金支出的{ratio}%，超过14%，"
        desc += "职工福利费超支可能导致税前扣除受限，同时反映企业可能存在隐性福利或虚列费用，需核实福利费支出的真实性与合规性。"
        return desc

    for profit in profits:
        if profit["period"] == "Year":
            year = profit["endDate"][:4]

            # 提取财务费用
            if profit["projectName"] == "管理费用":
                expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度财报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "应付职工薪酬":
                revenue = float(record["endingBalance"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 计算超支比例
            salary = annual_data[year]["expense_admin"]
            if salary > 0:
                ratio = (annual_data[year]["revenue"] / salary) * 100
                if ratio > 14:
                    risk_years[year] = round(ratio, 2)

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 格式化输出
    result = []
    for year in sorted_years:
        ratio = (
            annual_data[year]["revenue"] / annual_data[year]["expense_admin"]
            if annual_data[year]["expense_admin"]
            else 0
        )

        result.append(
            {
                "所属期": year,
                "职工福利费支出（元）": round(annual_data[year]["expense_admin"], 2),
                "工资薪金支出税收金额（元）": round(annual_data[year]["revenue"], 2),
                "职工福利费占工资薪金支出税收占比": f"{round(ratio, 2) * 100}%",
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.7
def advertising_expenses_analyzer(data):
    # 提取financeProfit数据
    profits = data["data"]["financeProfit"]
    income_records = data["data"]["financeBalance"]

    # 初始化一个字典来存储每年的营业收入和应纳所得税额
    annual_data = {}

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        desc = "风险描述："
        for year, ratio in risk_years.items():
            desc += f"{year}的广宣费支出占营业收入的{ratio}%，超过15%，"
        desc += "广宣费超支可能导致部分支出无法税前扣除，同时可能存在广宣费用虚列或广告投入转化率较低的问题，需核查广宣费支出的合理性和真实性。"
        return desc

    for profit in profits:
        if profit["period"] == "Year":
            year = profit["endDate"][:4]

            # 提取财务费用
            if profit["projectName"] == "销售费用":
                expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "expense_admin": expense_admin}
                else:
                    annual_data[year]["expense_admin"] = expense_admin

            # 提取营业收入
            if profit["projectName"] == "一、营业收入":
                revenue = float(profit["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 计算超支比例
            salary = annual_data[year]["expense_admin"] if year in annual_data else 0
            if salary > 0:
                ratio = (annual_data[year]["revenue"] / salary) * 100
                if ratio > 15:
                    risk_years[year] = round(ratio, 2)

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 格式化输出
    result = []
    for year in sorted_years:
        result.append(
            {
                "所属期": year,
                "广宣费支出（元）": round(annual_data[year]["expense_admin"], 2),
                "营业收入（元）": round(annual_data[year]["revenue"], 2),
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.8
def expense_revenue_analyzer(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的营业收入和营业成本
    annual_data = {}

    # 设置风险阈值
    RISK_THRESHOLD = 0.1

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, rate in risk_years.items():
            risk_desc += f"{year}年的营业成本占营业收入比例变动值为{round(rate, 2)}，超过±10%，"
        risk_desc += "营业成本占收入比例波动较大可能反映成本核算或收入确认异常，需关注企业成本管理是否有效，以及收入与成本数据的匹配性。"
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本":
                profit = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算营业成本占营业收入比例
    for year in sorted_years:
        if annual_data[year]["revenue"] != 0:
            annual_data[year]["expense_rate"] = (
                annual_data[year]["profit"] / annual_data[year]["revenue"]
            )
        else:
            annual_data[year]["expense_rate"] = "--"

    # 计算营业成本与营业收入比值变动值
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动值
            prev_year = sorted_years[i - 1]
            # 营业成本与营业收入比值变动值
            if (
                annual_data[prev_year]["revenue"] != 0
                and annual_data[year]["revenue"] != 0
            ):
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["profit"] / annual_data[year]["revenue"]
                    - annual_data[prev_year]["profit"]
                    / annual_data[prev_year]["revenue"]
                )
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动值
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None

    # 格式化输出
    result = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        profit = annual_data[year]["profit"]
        expense_rate = annual_data[year]["expense_rate"]
        period_expense_rate = annual_data[year]["period_expense_rate"]

        if period_expense_rate is not None and abs(period_expense_rate) > (
            RISK_THRESHOLD * 100
        ):
            risk_years[year] = period_expense_rate

        # 格式化
        if expense_rate == "--":
            expense_rate_formatted = "--"
        else:
            expense_rate_formatted = (
                f"{round(expense_rate * 100, 2)}%" if expense_rate is not None else "--"
            )
        period_expense_rate_formatted = (
            round(period_expense_rate, 2) if period_expense_rate is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": round(revenue, 2),
                "营业成本（元）": round(profit, 2),
                "营业成本占营业收入比例": expense_rate_formatted,
                "营业成本占营业收入比例变动值": period_expense_rate_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.9
def calculate_expense_revenue_elastic_modulus(data):
    # 提取financeProfit数据
    income_records = data["data"]["financeProfit"]

    # 初始化一个字典来存储每年的营业收入和营业成本
    annual_data = {}

    # 设置风险阈值范围
    RISK_THRESHOLD_MIN = 0.8
    RISK_THRESHOLD_MAX = 1.2

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的营业成本与营业收入变动系数为{round(elastic, 2)}，不在0.8 - 1.2之间，"
        risk_desc += "当营业成本变动与营业收入变动不匹配时，可能反映成本核算不合理或收入真实性存疑，需关注收入确认及成本归集的规范性。"
        return risk_desc

    # 遍历financeProfit数据
    for record in income_records:
        # 检查是否为年度表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入":
                revenue = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取营业成本
            if record["projectName"] == "减：营业成本":
                profit = float(record["currentYearAccumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 营业收入变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_growth_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 营业成本变动率
            if annual_data[prev_year]["profit"] != 0:
                annual_data[year]["profit_growth_rate"] = (
                    annual_data[year]["profit"] - annual_data[prev_year]["profit"]
                ) / annual_data[prev_year]["profit"]
            else:
                annual_data[year]["profit_growth_rate"] = None  # 上一年营业利润为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["revenue_growth_rate"] = None
            annual_data[year]["profit_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        if (
            sales_growth_rate is not None
            and profit_growth_rate is not None
            and profit_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / profit_growth_rate
            )
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        profit = annual_data[year]["profit"]
        revenue_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < RISK_THRESHOLD_MIN or elastic_modulus > RISK_THRESHOLD_MAX
        ):
            risk_years[year] = elastic_modulus

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        profit_growth_rate_formatted = (
            f"{round(profit_growth_rate * 100, 2)}%"
            if profit_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "营业成本（元）": profit,
                "营业收入变动率": revenue_growth_rate_formatted,
                "营业成本变动率": profit_growth_rate_formatted,
                "营业成本与营业收入变动系数": elastic_modulus_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.10
def calculate_revenue_period_expense_elastic_modulus(data):
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的营业收入和期间费用
    annual_data = {}

    # 设置风险阈值范围
    RISK_THRESHOLD_MIN = 0.8
    RISK_THRESHOLD_MAX = 1.2

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的营业收入与期间费用变动系数为{round(elastic, 2)}，不在0.8 - 1.2之间，"
        risk_desc += "期间费用与营业收入变动不匹配可能表明费用核算不准确或业务扩张与费用控制不当，需核查费用的合理性及其与收入的关联性。"
        return risk_desc

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "tax": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取管理费用
            if record["projectName"] == "减：管理费用（填写A104000）":
                expense_admin = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_admin"] = expense_admin
            # 提取销售费用
            if record["projectName"] == "减：销售费用（填写A104000）":
                expense_sales = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_sales"] = expense_sales
            # 提取财务费用
            if record["projectName"] == "减：财务费用（填写A104000）":
                expense_finance = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    pass
                else:
                    annual_data[year]["expense_finance"] = expense_finance

    # 计算期间费用（管理费用、销售费用、财务费用）
    for year in annual_data:
        annual_data[year]["period_expense"] = (
            annual_data[year].get("expense_admin", 0)
            + annual_data[year].get("expense_sales", 0)
            + annual_data[year].get("expense_finance", 0)
        )

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 期间费用变动率
            if annual_data[prev_year]["period_expense"] != 0:
                annual_data[year]["period_expense_rate"] = (
                    annual_data[year]["period_expense"]
                    - annual_data[prev_year]["period_expense"]
                ) / annual_data[prev_year]["period_expense"]
            else:
                annual_data[year]["period_expense_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 营业收入变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_rate"] = None  # 上一年应纳税额为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["period_expense_rate"] = None
            annual_data[year]["revenue_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["period_expense_rate"]
        tax_growth_rate = annual_data[year]["revenue_rate"]
        if (
            sales_growth_rate is not None
            and tax_growth_rate is not None
            and sales_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = tax_growth_rate / sales_growth_rate
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    for year in sorted_years:
        period_expense = annual_data[year]["period_expense"]
        revenue = annual_data[year]["revenue"]
        revenue_growth_rate = annual_data[year]["period_expense_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < RISK_THRESHOLD_MIN or elastic_modulus > RISK_THRESHOLD_MAX
        ):
            risk_years[year] = elastic_modulus

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        tax_growth_rate_formatted = (
            f"{round(tax_growth_rate * 100, 2)}%"
            if tax_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "期间费用（元）": round(period_expense, 2),
                "营业收入变动率": tax_growth_rate_formatted,
                "期间费用变动率": revenue_growth_rate_formatted,
                "营业收入与期间费用变动系数": elastic_modulus_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.11
def calculate_Tax_revenue_elastic_modulus(data):
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的纳税调整后所得和营业收入
    annual_data = {}

    # 设置风险阈值范围
    RISK_THRESHOLD_MIN = 0.8
    RISK_THRESHOLD_MAX = 1.2

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的纳税调整与营业收入弹性系数为{round(elastic, 2)}，不在0.8 - 1.2之间，"
        risk_desc += "可能预示着企业存在内部控制不严、数据记录不准确或税务筹划异常等问题，需进行进一步的风险预警和检查。"
        return risk_desc

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业收入
            if record["projectName"] == "一、营业收入（填写A101010\\101020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取纳税调整后所得
            if record["projectName"] == "四、纳税调整后所得（13-14+15-16-17+18）":
                profit = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 营业收入变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_growth_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

            # 纳税调整后所得变动率
            if annual_data[prev_year]["profit"] != 0:
                annual_data[year]["profit_growth_rate"] = (
                    annual_data[year]["profit"] - annual_data[prev_year]["profit"]
                ) / annual_data[prev_year]["profit"]
            else:
                annual_data[year]["profit_growth_rate"] = None  # 上一年纳税调整后所得为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["revenue_growth_rate"] = None
            annual_data[year]["profit_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        if (
            sales_growth_rate is not None
            and profit_growth_rate is not None
            and profit_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / profit_growth_rate
            )
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        profit = annual_data[year]["profit"]
        revenue_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < RISK_THRESHOLD_MIN or elastic_modulus > RISK_THRESHOLD_MAX
        ):
            risk_years[year] = elastic_modulus

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        profit_growth_rate_formatted = (
            f"{round(profit_growth_rate * 100, 2)}%"
            if profit_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业收入（元）": revenue,
                "纳税调整后所得（元）": profit,
                "纳税调整后所得变动率": profit_growth_rate_formatted,
                "营业收入变动率": revenue_growth_rate_formatted,
                "纳税调整与营业收入弹性系数": elastic_modulus_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description


# 7.12
def calculate_Tax_expense_elastic_modulus(data):
    # 提取corporateIncome数据
    income_records = data["data"]["corporateIncome"]

    # 初始化一个字典来存储每年的纳税调整后所得和营业成本
    annual_data = {}

    # 设置风险阈值范围
    RISK_THRESHOLD_MIN = 0.8
    RISK_THRESHOLD_MAX = 1.2

    # 添加风险年份记录
    risk_years = {}

    def generate_risk_description(risk_years):
        """生成风险描述"""
        if not risk_years:
            return "风险描述：该指标项未检测到风险"

        risk_desc = "风险描述："
        for year, elastic in risk_years.items():
            risk_desc += f"{year}年的纳税调整与营业成本弹性系数为{round(elastic, 2)}，不在0.8 - 1.2之间，"
        risk_desc += "可能表明企业存在内部数据记录不准确、内部控制缺陷或故意调整成本以达到税务筹划目的。"
        return risk_desc

    # 遍历corporateIncome数据
    for record in income_records:
        # 检查是否为年度纳税申报表
        if record["period"] == "Year":
            # 提取年份
            year = record["endDate"][:4]  # 从endDate中提取年份

            # 提取营业成本
            if record["projectName"] == "减：营业成本（填写A102010\\102020\\103000）":
                revenue = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": revenue, "profit": 0}
                else:
                    annual_data[year]["revenue"] = revenue

            # 提取纳税调整后所得
            if record["projectName"] == "四、纳税调整后所得（13-14+15-16-17+18）":
                profit = float(record["accumulativeAmount"] or 0)
                if year not in annual_data:
                    annual_data[year] = {"revenue": 0, "profit": profit}
                else:
                    annual_data[year]["profit"] = profit

    sorted_years = sorted(annual_data.keys(), reverse=False)

    # 计算变动率
    for i, year in enumerate(sorted_years):
        if i > 0:  # 从第二年开始计算变动率
            prev_year = sorted_years[i - 1]
            # 营业成本变动率
            if annual_data[prev_year]["revenue"] != 0:
                annual_data[year]["revenue_growth_rate"] = (
                    annual_data[year]["revenue"] - annual_data[prev_year]["revenue"]
                ) / annual_data[prev_year]["revenue"]
            else:
                annual_data[year]["revenue_growth_rate"] = None  # 上一年营业成本为 0，无法计算变动率

            # 纳税调整后所得变动率
            if annual_data[prev_year]["profit"] != 0:
                annual_data[year]["profit_growth_rate"] = (
                    annual_data[year]["profit"] - annual_data[prev_year]["profit"]
                ) / annual_data[prev_year]["profit"]
            else:
                annual_data[year]["profit_growth_rate"] = None  # 上一年纳税调整后所得为 0，无法计算变动率
        else:
            # 第一年没有上一年数据，变动率为 null
            annual_data[year]["revenue_growth_rate"] = None
            annual_data[year]["profit_growth_rate"] = None

        # 计算弹性系数
        sales_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        if (
            sales_growth_rate is not None
            and profit_growth_rate is not None
            and profit_growth_rate != 0
        ):
            annual_data[year]["elastic_modulus"] = (
                sales_growth_rate / profit_growth_rate
            )
        else:
            annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

    # 格式化输出
    result = []
    for year in sorted_years:
        revenue = annual_data[year]["revenue"]
        profit = annual_data[year]["profit"]
        revenue_growth_rate = annual_data[year]["revenue_growth_rate"]
        profit_growth_rate = annual_data[year]["profit_growth_rate"]
        elastic_modulus = annual_data[year]["elastic_modulus"]

        if elastic_modulus is not None and (
            elastic_modulus < RISK_THRESHOLD_MIN or elastic_modulus > RISK_THRESHOLD_MAX
        ):
            risk_years[year] = elastic_modulus

        # 格式化变动率和弹性系数
        revenue_growth_rate_formatted = (
            f"{round(revenue_growth_rate * 100, 2)}%"
            if revenue_growth_rate is not None
            else "--"
        )
        profit_growth_rate_formatted = (
            f"{round(profit_growth_rate * 100, 2)}%"
            if profit_growth_rate is not None
            else "--"
        )
        elastic_modulus_formatted = (
            round(elastic_modulus, 2) if elastic_modulus is not None else "--"
        )

        result.append(
            {
                "所属期": year,
                "营业成本（元）": revenue,
                "纳税调整后所得（元）": profit,
                "纳税调整后所得变动率": profit_growth_rate_formatted,
                "营业成本变动率": revenue_growth_rate_formatted,
                "纳税调整与营业成本弹性系数": elastic_modulus_formatted,
            }
        )

    risk_description = generate_risk_description(risk_years)

    return result, risk_description
